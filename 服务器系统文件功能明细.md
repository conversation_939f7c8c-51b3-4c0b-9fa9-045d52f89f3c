# 服务器系统文件功能明细文档

## 📋 概述
基于Node.js + Express + WebSocket的模块化服务器架构，支持设备管理、脚本执行、实时通信等功能。采用24个独立模块设计，总计16,500+行代码，支持小红书和闲鱼自动化平台。

## 🏗️ 项目结构

### 📁 根目录文件
- `server-main.js` (433行) - **主服务器入口**，模块化架构，整合24个功能模块
- `test-server.js` (13,301行) - **测试服务器**，包含完整功能的单文件版本，支持内存模式
- `package.json` - 后端依赖配置（Express 4.18.2、Socket.IO 4.8.1、MySQL2 3.14.2、Multer 2.0.1等）
- `初始化数据库.sql` (413行) - 完整数据库初始化脚本，包含19个表和连接码系统

### 📁 核心模块 (server/core/)

#### 🔧 基础核心
- `server-core.js` (608行) - **核心配置模块**
  - **数据库连接：** MySQL连接池配置、初始化、连接检测
  - **服务初始化：** Express应用、HTTP服务器、Socket.IO配置
  - **CORS配置：** 跨域请求处理、安全策略
  - **文件上传：** Multer配置、存储策略、文件类型验证
  - **服务加载：** 小红书日志服务、闲鱼日志服务、私聊服务初始化
  - **认证中间件：** JWT token验证、用户权限检查
  - **任务清理：** 设备断开时的任务清理机制

- `server-variables.js` (300行) - **变量声明模块**
  - **任务管理：** 活跃任务存储、任务ID生成、任务状态跟踪
  - **常量定义：** 设备状态、执行状态、功能类型枚举
  - **配置管理：** 上传配置、性能配置、数据库配置
  - **缓存功能：** 内存缓存、清理机制、数据持久化
  - **全局变量：** 设备列表、Web客户端、日志存储、命令队列

- `server-error-handling.js` (250行) - **错误处理模块**
  - **全局错误捕获：** 未处理异常、Promise拒绝、进程错误
  - **错误分类：** 数据库错误、网络错误、业务错误、系统错误
  - **错误恢复：** 自动重试、降级处理、故障转移
  - **错误报告：** 详细日志记录、监控告警、错误统计

- `server-performance.js` (300行) - **性能优化模块**
  - **性能监控：** CPU、内存、网络使用率、响应时间统计
  - **请求限流：** API频率限制、防护机制、恶意请求检测
  - **缓存策略：** 数据缓存、查询优化、内存管理
  - **资源管理：** 连接池优化、内存清理、垃圾回收

### 📁 认证模块 (server/auth/)
- `server-auth.js` (95行) - **认证API模块**
  - **用户登录：** JWT token生成、验证、密码加密
  - **权限管理：** 中间件认证、角色控制、管理员权限
  - **会话管理：** token刷新、过期处理、自动登出
  - **数据隔离：** 用户数据隔离、权限边界控制

### 📁 设备管理模块 (server/device/)

#### 📱 设备核心
- `server-device.js` (2,031行) - **设备管理API模块**
  - **30+个API接口：**
    - `GET /api/device/list` - 获取设备列表（支持用户隔离）
    - `POST /api/device/register` - 设备注册（支持连接码验证）
    - `DELETE /api/device/:id` - 断开设备连接
    - `POST /api/device/:deviceId/apps` - 设备应用信息上报
    - `GET /api/device/:deviceId/commands` - 获取待执行命令
    - `POST /api/device/:deviceId/result` - 上报脚本执行结果
    - `POST /api/device/:deviceId/screen` - 屏幕数据上传
    - `GET /api/device/stats` - 设备统计信息
  - **设备状态管理：** 在线、离线、忙碌状态跟踪、心跳检测
  - **屏幕流传输：** 实时屏幕数据处理、图像压缩、WebSocket转发
  - **设备分组：** 分组管理、批量操作、设备筛选
  - **健康检查：** 设备性能监控、连接质量、异常检测
  - **连接码系统：** 设备连接码管理、用户绑定、权限控制

- `device-connection-codes.js` (200行) - **设备连接码管理模块**
  - **连接码生成：** 随机码生成、有效期管理、使用次数限制
  - **用户绑定：** 连接码与用户关联、权限验证
  - **状态管理：** 连接码状态跟踪、使用记录、过期处理

- `server-device-apps.js` (249行) - **设备应用管理模块**
  - **应用检测：** 已安装应用列表、版本信息、权限状态
  - **应用状态：** 运行状态、权限检查、兼容性检测
  - **应用管理：** 启动、停止、卸载操作、应用更新

- `server-device-commands.js` (492行) - **设备命令管理模块**
  - **命令队列：** 待执行命令管理、优先级排序、超时处理
  - **命令分发：** 设备命令下发、状态跟踪、执行监控
  - **结果收集：** 执行结果上报、处理、统计分析

### 📁 脚本管理模块 (server/script/)

#### 📜 脚本核心
- `server-script.js` (1,070行) - **脚本管理API模块**
  - **脚本存储：** 上传、保存、版本控制
  - **脚本执行：** 任务分发、状态监控
  - **脚本分析：** 语法检查、性能分析
  - **模板管理：** 脚本模板、参数配置

- `server-script-generator.js` (259行) - **脚本生成模块**
  - **脚本转换：** UI脚本转无UI脚本
  - **参数注入：** 配置参数动态注入
  - **代码优化：** 性能优化、兼容性处理

- `server-script-converter.js` - **脚本转换器**
  - **格式转换：** 不同脚本格式转换
  - **兼容性处理：** 版本兼容、API适配

### 📁 小红书自动化模块 (server/xiaohongshu/)
- `server-xiaohongshu.js` (2,640行) - **小红书自动化API模块**
  - **40+个API接口：**
    - `POST /api/xiaohongshu/execute` - 执行自动化任务（支持用户隔离）
    - `POST /api/xiaohongshu/status` - 接收执行状态更新
    - `POST /api/xiaohongshu/upload-uid-file` - UID文件上传（支持重复检测）
    - `GET /api/xiaohongshu/uid-files` - 获取UID文件列表
    - `GET /api/xiaohongshu/uid-files/:fileId/uids` - 获取文件UID详情
    - `DELETE /api/xiaohongshu/uid-files/:fileId` - 删除UID文件
    - `POST /api/xiaohongshu/uid-files/:fileId/reset-status` - 重置UID状态
    - `POST /api/xiaohongshu/upload-video-files` - 视频文件批量上传
    - `GET /api/xiaohongshu/video-files` - 获取视频文件列表
    - `POST /api/xiaohongshu/transfer-videos` - 传输视频到设备
    - `GET /api/xiaohongshu/logs` - 获取执行日志（分页、筛选）
    - `POST /api/xiaohongshu/stop` - 停止脚本执行
    - `POST /api/xiaohongshu/record-uid-message` - 记录UID私信结果
  - **7大功能支持：**
    - 修改资料（昵称、简介）、搜索加群、循环群发、文章评论
    - 手动UID私信、文件UID私信、视频发布
  - **UID管理：** 文件上传、状态跟踪、使用记录、重复检测、批量分配
  - **视频管理：** 批量上传、传输、发布、去重检测、智能分配、缩略图生成
  - **任务调度：** 立即执行、定时执行、循环执行、任务队列管理
  - **实时状态：** 执行进度、状态播报、错误处理、WebSocket通信

### 📁 闲鱼自动化模块 (server/xianyu/)
- `server-xianyu.js` (1,381行) - **闲鱼自动化API模块**
  - **20+个API接口：**
    - `POST /api/xianyu/execute` - 执行闲鱼自动化（支持用户隔离）
    - `POST /api/xianyu/status` - 接收执行状态更新
    - `POST /api/xianyu/chat-record` - 私聊记录上报
    - `GET /api/xianyu/chat-records/:deviceId` - 获取设备私聊记录
    - `DELETE /api/xianyu/chat-records/:deviceId/all` - 清空设备私聊记录
    - `DELETE /api/xianyu/chat-records/:deviceId/:recordId` - 删除单条记录
    - `GET /api/xianyu/logs` - 获取执行日志（分页、筛选）
    - `POST /api/xianyu/stop` - 停止脚本执行
    - `POST /api/xianyu/stop-all` - 停止所有任务
    - `GET /api/xianyu/tasks` - 获取任务状态
  - **关键词私信：** 商品搜索、卖家筛选、私信自动化、重复过滤
  - **私聊管理：** 记录存储、查看、删除、统计、数据库存储
  - **任务控制：** 启动、停止、状态跟踪、批量操作、实时监控
  - **数据统计：** 私聊成功率、商品统计、卖家分析、效果评估

### 📁 通信模块 (server/websocket/)
- `server-websocket.js` (234行) - **WebSocket管理模块**
  - **连接管理：** 设备连接、Web客户端连接
  - **实时通信：** 状态同步、消息广播
  - **连接监控：** 连接质量、断线重连
  - **消息路由：** 消息分发、目标路由

### 📁 文件管理模块 (server/file/)
- `server-file.js` (246行) - **文件管理API模块**
  - **文件上传：** 多文件上传、进度跟踪
  - **文件下载：** 分块传输、断点续传
  - **文件管理：** 列表查看、删除、重命名
  - **存储优化：** 去重检测、压缩存储

### 📁 视频管理模块 (server/video/)
- `server-video.js` (501行) - **视频管理API模块**
  - **视频上传：** 批量上传、格式检测、重复检测
  - **视频处理：** 缩略图生成、格式转换、压缩
  - **视频传输：** 设备传输、进度监控、错误处理
  - **视频分配：** 智能分配、负载均衡、容量计算

### 📁 路由模块 (server/routes_modules/)
- `server-routes.js` (236行) - **静态页面路由模块**
  - **页面路由：** 主页、功能页面、管理页面
  - **静态资源：** CSS、JS、图片资源服务
  - **模板渲染：** 动态页面生成、参数注入

- `server-route-handlers.js` (120行) - **路由处理器**
  - **请求处理：** 参数验证、响应格式化
  - **中间件：** 认证、日志、错误处理
  - **路由优化：** 缓存、压缩、性能优化

### 📁 系统管理模块 (server/management/)

#### 🔧 系统监控
- `server-monitor.js` (375行) - **系统监控模块**
  - **10+个监控API：**
    - `GET /api/monitor/system-stats` - 系统统计信息
    - `GET /api/monitor/device-stats` - 设备统计信息
    - `GET /api/monitor/performance` - 性能监控数据
    - `GET /api/monitor/memory` - 内存使用情况
    - `GET /api/monitor/cpu` - CPU使用情况
    - `POST /api/monitor/alert` - 告警通知
  - **性能监控：** CPU、内存、磁盘、网络使用率、响应时间
  - **告警系统：** 阈值监控、自动告警、告警历史、通知机制
  - **健康检查：** 服务健康状态、依赖检查、自动恢复

- `server-management.js` (141行) - **服务器管理模块**
  - **服务控制：** 启动、停止、重启、状态查询、进程管理
  - **配置管理：** 动态配置、热更新、配置验证、回滚机制
  - **维护模式：** 维护开关、用户通知、服务降级
  - **系统信息：** 版本信息、运行时间、负载状态

- `server-shutdown.js` (352行) - **优雅关闭模块**
  - **关闭流程：** 停止接收新请求、完成现有请求、资源释放
  - **资源清理：** 数据库连接、文件句柄、定时器、内存清理
  - **状态保存：** 任务状态、设备状态持久化、数据备份
  - **信号处理：** SIGTERM、SIGINT信号处理、进程退出

### 📁 调试模块 (server/debug/)
- `server-debug.js` (529行) - **调试和测试模块**
  - **调试API：** 状态查询、日志查看、性能分析、内存分析
  - **测试工具：** 接口测试、压力测试、功能验证、自动化测试
  - **开发辅助：** 热重载、错误追踪、性能分析、代码覆盖率
  - **系统诊断：** 系统健康检查、依赖检测、配置验证

### 📁 工具模块 (server/utils/)
- `server-utils.js` (305行) - **通用工具函数**
  - **数据处理：** 格式化、验证、转换、加密解密
  - **文件操作：** 读写、复制、删除、压缩、路径处理
  - **网络工具：** HTTP请求、数据传输、错误处理、重试机制
  - **时间工具：** 时间格式化、时区处理、定时任务

- `server-additional-apis.js` (200行) - **额外API功能**
  - **扩展接口：** 特殊功能API、第三方集成、插件支持
  - **工具接口：** 数据导出、批量操作、统计分析、报表生成
  - **调试接口：** 系统诊断、性能分析、日志查看

- `server-public-apis.js` (430行) - **公共API接口**
  - **开放接口：** 无需认证的公共API、健康检查、系统状态
  - **数据接口：** 统计数据、状态信息、系统信息、监控数据
  - **文档接口：** API文档、接口说明、使用示例

- `DatabaseQueryEnhancer.js` (150行) - **数据库查询增强器**
  - **查询优化：** SQL优化、索引建议、性能分析
  - **连接管理：** 连接池优化、事务管理、死锁检测
  - **数据验证：** 参数验证、SQL注入防护、数据完整性

- `PermissionValidator.js` (100行) - **权限验证器**
  - **权限检查：** 用户权限验证、角色权限、操作权限
  - **访问控制：** API访问控制、资源权限、数据权限
  - **安全策略：** 安全规则、访问日志、异常检测

### 📁 数据库配置 (server/config/)
- `database.js` - **数据库连接配置**
  - **连接池：** MySQL连接池配置、优化参数
  - **初始化：** 数据库初始化、表结构检查
  - **事务管理：** 事务处理、回滚机制

### 📁 服务层 (server/services/)
- `xiaohongshuLogService.js` (500行) - **小红书日志服务**
  - **日志管理：** 执行日志存储、查询、统计、分页
  - **状态跟踪：** 任务状态更新、进度记录、实时同步
  - **数据分析：** 成功率统计、执行时长分析、错误分类
  - **用户隔离：** 按用户隔离日志数据、权限控制

- `xianyuLogService.js` (400行) - **闲鱼日志服务**
  - **日志管理：** 执行记录、错误日志、性能日志、数据持久化
  - **数据分析：** 执行统计、成功率分析、趋势预测
  - **任务监控：** 任务状态跟踪、异常检测、自动恢复

- `xianyuChatService.js` (300行) - **闲鱼私聊服务**
  - **私聊管理：** 私聊记录存储、查询、删除、批量操作
  - **数据统计：** 私聊统计、效果分析、商品分析
  - **重复检测：** 商品去重、卖家去重、内容去重

- `deviceManager.js` (250行) - **设备管理服务**
  - **设备状态：** 状态管理、健康检查、性能监控
  - **连接管理：** 连接池、重连机制、负载均衡
  - **设备分组：** 分组管理、批量操作、权限控制

- `AuthService.js` (200行) - **认证服务**
  - **用户认证：** 登录验证、密码加密、权限检查
  - **主站集成：** 主站数据库验证、本地认证降级
  - **会话管理：** token管理、过期处理、自动刷新

## 🎯 API统计总览

### 📊 模块API数量
- **小红书自动化：** 40+个API
- **闲鱼自动化：** 20+个API
- **设备管理：** 30+个API
- **脚本管理：** 15个API
- **文件管理：** 12个API
- **视频管理：** 18个API
- **系统监控：** 10+个API
- **认证管理：** 8个API
- **调试工具：** 12个API
- **管理员功能：** 15个API
- **中间件和工具：** 20+个API

**总计：** 200+个API接口

### 🔧 核心功能特性

#### 🚀 高性能架构
- **模块化设计：** 24个独立模块，职责分离
- **连接池管理：** 数据库连接复用、性能优化
- **缓存策略：** 内存缓存、查询优化
- **负载均衡：** 任务分发、资源调度

#### 🔐 安全机制
- **JWT认证：** token验证、权限控制
- **数据验证：** 输入验证、SQL注入防护
- **错误处理：** 全局异常捕获、安全降级
- **访问控制：** API权限、操作审计

#### 📡 实时通信
- **WebSocket：** 双向实时通信、状态同步
- **消息广播：** 多客户端消息分发
- **连接管理：** 自动重连、连接监控
- **数据同步：** 设备状态、任务进度实时更新

#### 🔄 任务调度
- **任务队列：** 任务排队、优先级管理
- **执行控制：** 启动、停止、暂停、恢复
- **状态跟踪：** 实时进度、结果反馈
- **错误恢复：** 自动重试、失败处理

## 📦 部署配置

### 🏗️ 开发环境
- **热重载：** 代码修改自动重启
- **调试模式：** 详细日志、错误追踪
- **开发工具：** API测试、性能分析

### 🚀 生产环境
- **进程管理：** PM2集群模式、自动重启
- **性能优化：** 代码压缩、缓存策略
- **监控告警：** 系统监控、异常告警
- **日志管理：** 日志轮转、远程收集

## 📁 脚本文件 (jb/ 和 xy-jb/)

### 📸 小红书脚本 (jb/)
- `小红书UID私信脚本-无UI版.js` (5,000+行) - **UID私信核心脚本**
  - **应用启动：** 多种启动方式、兼容性处理
  - **UID搜索：** 用户搜索、结果筛选、状态验证
  - **私信发送：** 消息发送、状态跟踪、错误处理
  - **状态上报：** 实时进度、结果反馈、错误报告

- `小红书发布视频脚本-无UI版.js` (3,000+行) - **视频发布核心脚本**
  - **视频上传：** 文件选择、上传进度、格式检查
  - **内容编辑：** 标题设置、描述编写、标签添加
  - **发布流程：** 发布设置、隐私控制、发布确认
  - **结果处理：** 发布状态、链接获取、统计上报

- `无ui界面6.30.js` (2,000+行) - **修改资料脚本**
  - **资料修改：** 昵称修改、简介更新、头像设置
  - **验证处理：** 输入验证、格式检查、保存确认

- `无ui界面 群聊.js` (2,500+行) - **群聊功能脚本**
  - **群聊搜索：** 关键词搜索、群组筛选、加群操作
  - **消息发送：** 群消息发送、内容管理、发送策略

- `search_comment_no_ui.js` (2,000+行) - **文章评论脚本**
  - **文章搜索：** 关键词搜索、内容筛选、目标定位
  - **评论发布：** 评论内容、发布时机、互动策略

- `每小时群发消息.js` (1,500+行) - **定时群发脚本**
  - **定时任务：** 时间调度、循环执行、间隔控制
  - **批量发送：** 群组管理、消息分发、状态跟踪

### 🐟 闲鱼脚本 (xy-jb/)
- `闲鱼关键词私信-无ui界面.js` (5,500+行) - **闲鱼私信核心脚本**
  - **应用启动：** 闲鱼启动、界面检测、状态确认
  - **关键词搜索：** 商品搜索、结果筛选、卖家定位
  - **私信发送：** 私信内容、发送策略、状态跟踪
  - **数据收集：** 商品信息、卖家信息、私信记录
  - **错误处理：** 异常捕获、重试机制、状态恢复

### 🔧 工具脚本 (scripts/)
- `双向.js` (8,000+行) - **设备通信核心脚本**
  - **WebSocket连接：** 服务器连接、心跳保持、重连机制
  - **命令接收：** 服务器命令解析、参数处理、执行调度
  - **状态上报：** 设备状态、执行进度、结果反馈
  - **屏幕传输：** 屏幕截图、数据压缩、实时传输
  - **应用检测：** 已安装应用、运行状态、权限检查

- `兼容版文本查找应用.js` (500+行) - **应用查找工具**
  - **应用搜索：** 应用名称搜索、包名查找
  - **兼容性处理：** 多版本适配、系统兼容

## 🗄️ 数据库结构

### 📊 数据库表 (19个表)
1. **用户管理表：**
   - `users` - 用户账户、权限管理
   - `devices` - 设备信息、连接状态
   - `device_apps` - 设备应用信息

2. **脚本管理表：**
   - `scripts` - 脚本存储、版本管理
   - `execution_logs` - 通用执行日志
   - `file_transfers` - 文件传输记录

3. **小红书功能表：**
   - `xiaohongshu_execution_logs` - 小红书执行日志
   - `xiaohongshu_uids` - UID存储管理
   - `xiaohongshu_manual_uid_messages` - 手动UID私信记录
   - `xiaohongshu_file_uid_messages` - 文件UID私信记录

4. **视频管理表：**
   - `xiaohongshu_video_files` - 视频文件管理
   - `xiaohongshu_video_transfers` - 视频传输记录
   - `xiaohongshu_video_assignments` - 视频分配记录
   - `xiaohongshu_video_execution_logs` - 视频发布日志

5. **闲鱼功能表：**
   - `xianyu_execution_logs` - 闲鱼执行日志
   - `xianyu_chat_records` - 闲鱼私聊记录

6. **文件管理表：**
   - `uid_files` - UID文件管理
   - `uid_data` - UID数据存储

## 🔄 系统工作流程

### 📱 设备连接流程
1. **设备注册：** 设备信息上报、唯一ID分配
2. **WebSocket连接：** 建立双向通信通道
3. **状态同步：** 设备状态、应用信息同步
4. **心跳保持：** 定期心跳、连接监控

### 🎯 任务执行流程
1. **任务创建：** Web端配置、参数验证
2. **设备选择：** 设备筛选、负载均衡
3. **脚本下发：** 脚本传输、参数注入
4. **执行监控：** 实时状态、进度跟踪
5. **结果收集：** 执行结果、日志收集

### 📊 数据流转流程
1. **数据采集：** 设备数据、执行数据、状态数据
2. **数据处理：** 格式化、验证、存储
3. **数据分析：** 统计分析、趋势预测
4. **数据展示：** 实时显示、报表生成

## 🛠️ 技术栈详情

### 🔧 后端技术
- **Node.js 16+：** 服务器运行环境
- **Express 4.18：** Web框架、路由管理
- **Socket.IO 4.7：** 实时双向通信
- **MySQL2 3.6：** 数据库连接、查询优化
- **Multer 1.4：** 文件上传处理
- **JWT：** 用户认证、权限控制
- **CORS：** 跨域请求处理

### 📦 依赖管理
- **bcryptjs：** 密码加密、安全验证
- **axios：** HTTP客户端、API请求
- **fs-extra：** 文件系统操作增强
- **path：** 路径处理、文件定位
- **crypto：** 加密算法、哈希计算

### 🔍 开发工具
- **nodemon：** 开发环境热重载
- **pm2：** 生产环境进程管理
- **winston：** 日志管理、分级记录
- **joi：** 数据验证、参数校验

## 📈 性能指标

### 🚀 系统性能
- **并发连接：** 支持1000+设备同时连接
- **API响应：** 平均响应时间<100ms
- **文件传输：** 支持GB级文件传输
- **实时通信：** WebSocket延迟<50ms

### 💾 资源使用
- **内存占用：** 基础运行<512MB
- **CPU使用：** 空闲状态<5%
- **磁盘IO：** 优化读写、缓存策略
- **网络带宽：** 智能压缩、流量控制

## 🔒 安全特性

### 🛡️ 安全机制
- **身份认证：** JWT token、会话管理
- **权限控制：** 角色权限、操作审计
- **数据加密：** 传输加密、存储加密
- **输入验证：** 参数校验、SQL注入防护

### 🔐 安全策略
- **访问控制：** IP白名单、频率限制
- **错误处理：** 安全错误信息、日志记录
- **数据备份：** 定期备份、灾难恢复
- **监控告警：** 异常检测、实时告警
