const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const db = require('../config/database');

// 小红书自动化任务存储
let activeTasks = new Map();
let taskHistory = [];

// 测试路由
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: '小红书自动化API正常工作',
    timestamp: new Date().toISOString(),
    routes: [
      'GET /api/xiaohongshu/test',
      'POST /api/xiaohongshu/realtime-status',
      'POST /api/xiaohongshu/execute',
      'POST /api/xiaohongshu/stop',
      'GET /api/xiaohongshu/tasks'
    ]
  });
});

// 处理实时状态上报
router.post('/realtime-status', (req, res) => {
  try {
    const statusData = req.body;
    console.log('📊 收到实时状态上报:', {
      deviceId: statusData.deviceId,
      taskId: statusData.taskId,
      currentStatus: statusData.currentStatus,
      operationCount: statusData.operationCount,
      processedStepCount: statusData.processedStepCount,
      message: statusData.message
    });

    // 验证必要参数
    if (!statusData.deviceId || !statusData.taskId) {
      console.error('❌ 实时状态上报缺少必要参数:', {
        deviceId: statusData.deviceId,
        taskId: statusData.taskId
      });
      return res.status(400).json({
        success: false,
        message: '缺少设备ID或任务ID'
      });
    }

    // 获取WebSocket实例
    const io = req.app.get('io');
    console.log('🔍 检查WebSocket实例:', io ? '可用' : '不可用');

    if (io) {
      // 检查连接的客户端数量
      const clientCount = io.engine.clientsCount;
      console.log('📱 当前连接的客户端数量:', clientCount);

      // 向所有连接的客户端广播实时状态更新
      io.emit('xiaohongshu_realtime_status', statusData);
      console.log('✅ 实时状态已广播到所有客户端:', {
        deviceId: statusData.deviceId,
        taskId: statusData.taskId,
        currentStatus: statusData.currentStatus,
        message: statusData.message,
        clientCount: clientCount
      });

      // 发送一个测试事件确认WebSocket工作正常
      io.emit('test_realtime_broadcast', {
        message: '测试实时广播',
        timestamp: new Date().toISOString(),
        originalData: statusData
      });
      console.log('🧪 测试广播已发送');

    } else {
      console.warn('⚠️ WebSocket实例不可用，无法广播实时状态');
    }

    res.json({
      success: true,
      message: '实时状态已接收并广播'
    });

  } catch (error) {
    console.error('❌ 处理实时状态上报失败:', error);
    res.status(500).json({
      success: false,
      message: '处理实时状态失败',
      error: error.message
    });
  }
});

// 执行小红书自动化任务
router.post('/execute', async (req, res) => {
  try {
    const { functionType, config, schedule, deviceIds: devices } = req.body;

    console.log('收到小红书自动化执行请求:', {
      function: functionType,
      devices: devices.length,
      schedule: schedule.mode
    });

    // 验证参数
    if (!functionType || !devices || devices.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 根据功能类型选择对应的脚本文件
    const scriptMap = {
      'profile': '../../jb/无ui界面6.30.js',
      'searchGroupChat': '../../jb/无ui界面 群聊.js',
      'groupMessage': '../../jb/每小时群发消息.js',
      'articleComment': '../../jb/search_comment_no_ui.js',
      'uidMessage': '../../jb/小红书UID私信脚本-无UI版.js',
      'uidFileMessage': '../../jb/小红书UID私信脚本-无UI版.js',
      'videoPublish': '../../jb/小红书发布视频脚本-无UI版.js'
    };

    const scriptPath = path.join(__dirname, scriptMap[functionType]);

    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        message: `${functionType}功能脚本不存在`
      });
    }

    const scriptContent = fs.readFileSync(scriptPath, 'utf8');

    // 创建任务ID
    const taskId = 'xiaohongshu_' + functionType + '_' + Date.now();

    // 构建执行参数
    const executeParams = {
      function: functionType,
      config: config,
      schedule: schedule
    };

    // 根据执行模式处理
    if (schedule.mode === 'immediate') {
      // 立即执行
      await executeImmediately(taskId, scriptContent, executeParams, devices, req.io, req);
    } else if (schedule.mode === 'scheduled') {
      // 定时执行
      scheduleTask(taskId, scriptContent, executeParams, devices, schedule.scheduledTime, req.io, req);
    } else if (schedule.mode === 'loop') {
      // 循环执行
      scheduleLoopTask(taskId, scriptContent, executeParams, devices, schedule.interval, req.io, req);
    }

    // 记录任务
    const task = {
      id: taskId,
      function: functionType,
      config: config,
      devices: devices,
      schedule: schedule,
      status: 'running',
      createdAt: new Date(),
      startedAt: new Date()
    };

    activeTasks.set(taskId, task);
    taskHistory.unshift(task);

    // 限制历史记录数量
    if (taskHistory.length > 100) {
      taskHistory = taskHistory.slice(0, 100);
    }

    res.json({
      success: true,
      message: '小红书自动化任务已启动',
      taskId: taskId
    });

  } catch (error) {
    console.error('执行小红书自动化任务失败:', error);
    res.status(500).json({
      success: false,
      message: '执行失败: ' + error.message
    });
  }
});

// 立即执行任务
async function executeImmediately(taskId, scriptContent, params, devices, io, req = null) {
  console.log('立即执行小红书任务:', taskId);

  for (const deviceId of devices) {
    try {
      // 获取设备连接 - 通过io查找连接的设备
      let deviceSocket = null;

      // 遍历所有socket连接查找设备
      if (io && io.sockets && io.sockets.sockets) {
        for (const [socketId, socket] of io.sockets.sockets) {
          if (socket.deviceId === deviceId) {
            deviceSocket = socket;
            break;
          }
        }
      }

      if (!deviceSocket) {
        console.log('设备未连接:', deviceId);

        // 通知Web客户端设备未连接
        if (io) {
          io.emit('xiaohongshu_task_update', {
            taskId: taskId,
            deviceId: deviceId,
            status: 'failed',
            message: `设备 ${deviceId} 未连接`
          });
        }
        continue;
      }

      // 根据功能类型构建特定的配置参数
      let functionConfig = buildFunctionConfig(params.function, params.config);

      // 如果是UID私信且使用文件模式，先分配UID
      if ((params.function === 'uidMessage' && functionConfig.inputMode === 'file' && functionConfig.selectedFileId) ||
          (params.function === 'uidFileMessage' && functionConfig.selectedFileId)) {
        try {
          console.log('=== UID私信文件模式，开始分配UID ===');
          console.log('文件ID:', functionConfig.selectedFileId);
          console.log('设备数量:', devices.length);
          console.log('总UID数量:', functionConfig.totalUidCount);

          // 直接调用数据库分配UID
          const allocationResult = await allocateUidsFromDatabase(
            functionConfig.selectedFileId,
            devices.length,
            functionConfig.totalUidCount,
            taskId
          );

          if (allocationResult.success) {
            console.log('UID分配成功:', allocationResult.data);
            
            // 更新配置，为每个设备分配对应的UID
            const deviceAllocations = allocationResult.data.deviceAllocations;
            const deviceIndex = devices.indexOf(deviceId);
            const deviceAllocation = deviceAllocations[deviceIndex];
            
            if (deviceAllocation) {
              functionConfig.uidList = deviceAllocation.uids;
              functionConfig.allocatedUidIds = deviceAllocation.uidIds;
              console.log(`设备 ${deviceId} 分配到的UID:`, deviceAllocation.uids);
            } else {
              console.warn(`设备 ${deviceId} 未分配到UID`);
              functionConfig.uidList = [];
            }
          } else {
            console.error('UID分配失败:', allocationResult.message);
            // 分配失败时使用空UID列表
            functionConfig.uidList = [];
          }
        } catch (error) {
          console.error('UID分配过程出错:', error);
          functionConfig.uidList = [];
        }
      }

      // 如果是视频发布功能，记录视频传输到数据库
      if (params.function === 'videoPublish' && functionConfig.selectedVideos) {
        try {
          console.log('🚀🚀🚀 [xiaohongshu.js] 准备记录视频传输到数据库 🚀🚀🚀');
          console.log('📹 [xiaohongshu.js] 选中的视频:', functionConfig.selectedVideos);
          console.log('📱 [xiaohongshu.js] 设备ID:', deviceId);
          console.log('🆔 [xiaohongshu.js] 任务ID:', taskId);

          // 调用test-server.js中的函数
          if (global.recordVideoTransfersForScript) {
            await global.recordVideoTransfersForScript(functionConfig.selectedVideos, deviceId, taskId, 'pending');
            console.log('✅ [xiaohongshu.js] 视频传输记录完成（状态：pending）');
          } else {
            console.log('⚠️ [xiaohongshu.js] recordVideoTransfersForScript函数不可用');
          }
        } catch (error) {
          console.error('❌ [xiaohongshu.js] 记录视频传输失败:', error);
        }
      }

      // 为无UI脚本注入参数
      let finalScript = scriptContent;
      if (params.function === 'profile') {
        // 修改资料功能：直接使用无UI脚本，注入参数并替换main函数调用
        console.log('=== 修改资料脚本调试信息 ===');
        console.log('原始脚本长度:', scriptContent.length);
        console.log('功能配置参数:', JSON.stringify(functionConfig, null, 2));

        // 移除原始的main()调用和getUserInput()调用
        let modifiedScript = scriptContent.replace(/main\(\);?\s*$/m, '');

        // 在脚本开头注入参数并直接调用executeScript
        const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(functionConfig, null, 2)};

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + serverParams.onlyNickname);
console.log("只修改简介: " + serverParams.onlyProfile);

try {
    executeScript(serverParams.nickname, serverParams.profile, serverParams.onlyNickname, serverParams.onlyProfile, serverParams);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
}

`;
        finalScript = modifiedScript + '\n' + paramInjection;

        console.log('最终脚本长度:', finalScript.length);
        console.log('参数注入成功，参数内容:', JSON.stringify(functionConfig, null, 2));
      } else if (params.function === 'searchGroupChat') {
        // 调试：输出脚本信息
        console.log('=== 搜索群聊脚本调试信息 ===');
        console.log('原始脚本长度:', scriptContent.length);
        console.log('原始脚本前200字符:', scriptContent.substring(0, 200));
        console.log('脚本是否包含坐标点击:', scriptContent.includes('click(clickX, clickY)'));
        console.log('脚本是否包含无UI界面群聊:', scriptContent.includes('无ui界面 群聊'));
        console.log('脚本是否包含main函数:', scriptContent.includes('function main()'));

        // 在脚本开头注入serverParams变量
        const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(functionConfig, null, 2)};

`;
        finalScript = paramInjection + scriptContent;

        console.log('最终脚本长度:', finalScript.length);
        console.log('参数注入成功，参数内容:', JSON.stringify(functionConfig, null, 2));
      } else if (params.function === 'articleComment') {
        // 文章评论功能：直接使用无UI脚本，注入参数并替换CONFIG对象
        console.log('=== 文章评论脚本调试信息 ===');
        console.log('原始脚本长度:', scriptContent.length);
        console.log('功能配置参数:', JSON.stringify(functionConfig, null, 2));

        // 替换脚本中的CONFIG对象
        let modifiedScript = scriptContent.replace(
          /\/\/ 脚本配置参数\s*\nconst CONFIG = \{[\s\S]*?\};/,
          `// 脚本配置参数
const CONFIG = {
    keyword: "${functionConfig.searchKeyword || '美食推荐'}",
    commentCount: ${functionConfig.commentCount || 3},
    delay: ${functionConfig.operationDelay || 5},
    commentMode: "${functionConfig.commentMode || 'emoji'}",
    commentText: "${functionConfig.commentText || '很棒的分享！'}"
};`
        );

        // 在脚本末尾添加执行调用
        const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(functionConfig, null, 2)};

// 直接执行脚本
console.log("=== 小红书文章评论脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("搜索关键词: " + CONFIG.keyword);
console.log("评论文章数量: " + CONFIG.commentCount);
console.log("操作间隔: " + CONFIG.delay + "秒");

// 设置运行状态
isRunning = true;

try {
    executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
} finally {
    isRunning = false;
    console.log("=== 脚本执行结束 ===");
}
`;

        finalScript = modifiedScript + executeCall;

        console.log('最终脚本长度:', finalScript.length);
        console.log('参数注入成功，参数内容:', JSON.stringify(functionConfig, null, 2));
      }

      // 发送脚本执行命令
      const executeData = {
        logId: taskId + '_' + deviceId,
        script: finalScript,
        params: functionConfig
      };

      deviceSocket.emit('execute_script', executeData);

      // 通知Web客户端任务状态
      if (io) {
        io.emit('xiaohongshu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'executing',
          message: `正在执行${getFunctionName(params.function)}任务`
        });
      }

      console.log('已向设备发送小红书任务:', deviceId, params.function);

    } catch (error) {
      console.error('向设备发送任务失败:', deviceId, error);

      if (io) {
        io.emit('xiaohongshu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'failed',
          message: '发送任务失败: ' + error.message
        });
      }
    }
  }
}

// 构建功能特定的配置参数
function buildFunctionConfig(functionType, config) {
  switch (functionType) {
    case 'profile':
      return {
        nickname: config.nickname || '',
        profile: config.profile || '',
        onlyNickname: config.modifyOptions?.includes('onlyNickname') || false,
        onlyProfile: config.modifyOptions?.includes('onlyProfile') || false,
        autoSave: config.modifyOptions?.includes('autoSave') || false,
        operationDelay: config.operationDelay || 2,
        safetyOptions: config.safetyOptions || [],
        // 安全设置的具体选项
        backupOriginal: config.safetyOptions?.includes('backupOriginal') || false,
        confirmBeforeChange: config.safetyOptions?.includes('confirmBeforeChange') || false,
        validateInput: config.safetyOptions?.includes('validateInput') || false
      };

    case 'searchGroupChat':
      return {
        searchKeyword: config.searchKeyword || '私域',
        targetJoinCount: config.targetJoinCount || 5,
        maxScrollAttempts: config.maxScrollAttempts || 10,
        enableDetailedLog: config.enableDetailedLog || false
      };

    case 'groupMessage':
      return {
        messageContent: config.messageContent || '',
        sendInterval: config.sendInterval || 10,
        executionMode: config.executionMode || 'once',
        loopInterval: config.loopInterval || 60,
        randomDelay: config.sendSettings?.includes('randomDelay') || true
      };

    case 'articleComment':
      return {
        searchKeyword: config.searchKeyword || '美食推荐',
        commentCount: config.commentCount || 3,
        operationDelay: config.operationDelay || 5,
        commentType: config.commentType || 'template',
        customComments: config.customComments || '',
        commentTemplates: config.commentTemplates || ['praise'],
        // 新增评论模式配置
        commentMode: config.commentMode || 'emoji',
        commentText: config.commentText || '很棒的分享！'
      };

    case 'uidMessage':
      return {
        inputMode: config.inputMode || 'manual',
        uidList: config.uidList || [],
        selectedFileId: config.selectedFileId || null,
        uidsPerDevice: config.uidsPerDevice || 5,
        totalUidCount: config.totalUidCount || 10,
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog || false,
        skipUsedUids: config.skipUsedUids || false,
        autoMarkUsed: config.autoMarkUsed || false
      };

    case 'uidFileMessage':
      return {
        inputMode: 'file', // 文件上传模式固定为file
        uidList: config.uidList || [],
        selectedFileId: config.selectedFileId || null,
        uidsPerDevice: config.uidsPerDevice || 5,
        totalUidCount: config.totalUidCount || 10,
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog || false,
        skipUsedUids: config.skipUsedUids || false,
        autoMarkUsed: config.autoMarkUsed || false
      };

    case 'videoPublish':
      return {
        titleTemplate: config.titleTemplate || '',
        selectedApp: config.selectedApp || '小红书',
        selectedVideos: config.selectedVideos || [],
        selectedVideoIds: config.selectedVideoIds || []
      };

    default:
      return config;
  }
}

// 获取功能名称
function getFunctionName(functionType) {
  const nameMap = {
    'profile': '修改资料',
    'searchGroupChat': '搜索加群',
    'groupMessage': '循环群发',
    'articleComment': '文章评论',
    'uidMessage': '手动输入UID私信',
    'uidFileMessage': '文件上传UID私信',
    'videoPublish': '视频发布'
  };
  return nameMap[functionType] || functionType;
}

// 从数据库分配UID
async function allocateUidsFromDatabase(fileId, deviceCount, totalCount, taskId) {
  try {
    const connection = await db.pool.getConnection();
    
    try {
      // 检查可用UID数量
      const [availableResult] = await connection.execute(
        'SELECT COUNT(*) as count FROM uid_data WHERE file_id = ? AND is_used = 0',
        [fileId]
      );

      const availableCount = availableResult[0].count;
      
      if (availableCount < totalCount) {
        return {
          success: false,
          message: `可用UID数量不足。需要: ${totalCount}, 可用: ${availableCount}`
        };
      }

      // 获取可用的UID
      const [uids] = await connection.execute(
        'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 LIMIT ?',
        [fileId, totalCount]
      );

      // 按设备数量分配UID
      const deviceAllocations = [];
      const uidsPerDevice = Math.ceil(totalCount / deviceCount);
      
      for (let i = 0; i < deviceCount; i++) {
        const startIndex = i * uidsPerDevice;
        const endIndex = Math.min(startIndex + uidsPerDevice, uids.length);
        const deviceUids = uids.slice(startIndex, endIndex);
        
        if (deviceUids.length > 0) {
          deviceAllocations.push({
            deviceIndex: i,
            uids: deviceUids.map(u => u.uid),
            uidIds: deviceUids.map(u => u.id)
          });
        }
      }

      // 标记UID为已使用
      const uidIds = uids.map(u => u.id);
      if (uidIds.length > 0) {
        const placeholders = uidIds.map(() => '?').join(',');
        await connection.execute(
          `UPDATE uid_data SET is_used = 1, used_time = NOW(), task_id = ? WHERE id IN (${placeholders})`,
          [taskId, ...uidIds]
        );
      }

      connection.release();

      return {
        success: true,
        message: 'UID分配成功',
        data: {
          totalAllocated: uids.length,
          deviceAllocations: deviceAllocations
        }
      };

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('分配UID失败:', error);
    return {
      success: false,
      message: '分配UID失败: ' + error.message
    };
  }
}

// 定时执行任务
function scheduleTask(taskId, scriptContent, params, devices, scheduledTime, io, req = null) {
  const executeTime = new Date(scheduledTime);
  const now = new Date();
  const delay = executeTime.getTime() - now.getTime();

  if (delay <= 0) {
    console.log('定时时间已过，立即执行:', taskId);
    executeImmediately(taskId, scriptContent, params, devices, io, req);
    return;
  }

  console.log('定时执行小红书任务:', taskId, '延迟:', delay + 'ms');

  setTimeout(() => {
    executeImmediately(taskId, scriptContent, params, devices, io, req);
  }, delay);
}

// 循环执行任务
function scheduleLoopTask(taskId, scriptContent, params, devices, intervalMinutes, io, req = null) {
  console.log('循环执行小红书任务:', taskId, '间隔:', intervalMinutes + '分钟');

  const intervalMs = intervalMinutes * 60 * 1000;

  // 立即执行第一次
  executeImmediately(taskId, scriptContent, params, devices, io, req);

  // 设置循环执行
  const intervalId = setInterval(() => {
    const task = activeTasks.get(taskId);
    if (!task || task.status === 'stopped') {
      clearInterval(intervalId);
      return;
    }

    executeImmediately(taskId, scriptContent, params, devices, io, req);
  }, intervalMs);

  // 保存定时器ID以便停止
  const task = activeTasks.get(taskId);
  if (task) {
    task.intervalId = intervalId;
  }
}

// 停止所有任务
router.post('/stop', async (req, res) => {
  try {
    console.log('停止所有小红书自动化任务');

    // 停止所有活跃任务
    for (const [taskId, task] of activeTasks) {
      task.status = 'stopped';
      task.stoppedAt = new Date();

      // 清除定时器
      if (task.intervalId) {
        clearInterval(task.intervalId);
      }

      // 通知设备停止执行
      for (const deviceId of task.devices) {
        // 通过io查找设备连接
        let deviceSocket = null;
        if (req.io && req.io.sockets && req.io.sockets.sockets) {
          for (const [socketId, socket] of req.io.sockets.sockets) {
            if (socket.deviceId === deviceId) {
              deviceSocket = socket;
              break;
            }
          }
        }

        if (deviceSocket) {
          deviceSocket.emit('stop_script', { taskId: taskId });
        }
      }
    }

    // 清空活跃任务
    activeTasks.clear();

    // 通知Web客户端
    if (req.io) {
      req.io.emit('xiaohongshu_all_tasks_stopped');
    }

    res.json({
      success: true,
      message: '已停止所有小红书自动化任务'
    });

  } catch (error) {
    console.error('停止小红书任务失败:', error);
    res.status(500).json({
      success: false,
      message: '停止失败: ' + error.message
    });
  }
});

// 获取任务状态
router.get('/tasks', (req, res) => {
  try {
    const activeTasks_array = Array.from(activeTasks.values());

    res.json({
      success: true,
      data: {
        activeTasks: activeTasks_array,
        taskHistory: taskHistory.slice(0, 50) // 返回最近50个任务
      }
    });

  } catch (error) {
    console.error('获取小红书任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// UID管理相关API接口
const multer = require('multer');
const mysql = require('mysql2/promise');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/uids');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    cb(null, `${timestamp}_${originalName}`);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.txt', '.csv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 .txt 和 .csv 格式的文件'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'autojs_control',
  password: 'root',
  database: 'autojs_control'
};

// 上传UID文件
router.post('/upload-uids', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;
    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');

    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length === 0) {
      fs.unlinkSync(filePath); // 删除空文件
      return res.status(400).json({
        success: false,
        message: '文件内容为空或格式不正确'
      });
    }

    // 连接数据库
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 批量插入UID到数据库
      const insertPromises = lines.map(uid => {
        return connection.execute(
          'INSERT INTO xiaohongshu_uids (uid, file_name, upload_time) VALUES (?, ?, NOW())',
          [uid, originalName]
        );
      });

      await Promise.all(insertPromises);

      res.json({
        success: true,
        message: `成功上传 ${lines.length} 个UID`,
        data: {
          fileName: originalName,
          uidCount: lines.length
        }
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('上传UID文件失败:', error);
    res.status(500).json({
      success: false,
      message: '上传失败: ' + error.message
    });
  }
});

// 获取已上传的UID文件列表
router.get('/uid-files', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig);

    try {
      const [rows] = await connection.execute(`
        SELECT
          file_name as fileName,
          COUNT(*) as totalCount,
          SUM(CASE WHEN is_used = 1 THEN 1 ELSE 0 END) as usedCount,
          MIN(upload_time) as uploadTime
        FROM xiaohongshu_uids
        GROUP BY file_name
        ORDER BY uploadTime DESC
      `);

      res.json({
        success: true,
        data: rows
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('获取UID文件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 获取指定文件的UID详情
router.get('/uid-files/:fileName/uids', async (req, res) => {
  try {
    const { fileName } = req.params;
    const connection = await mysql.createConnection(dbConfig);

    try {
      const [rows] = await connection.execute(`
        SELECT id, uid, is_used as isUsed, used_time as usedTime,
               used_device_id as usedDeviceId, file_name as fileName
        FROM xiaohongshu_uids
        WHERE file_name = ?
        ORDER BY upload_time ASC
      `, [fileName]);

      res.json({
        success: true,
        data: rows
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('获取UID详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 删除UID文件
router.delete('/uid-files/:fileName', async (req, res) => {
  try {
    const { fileName } = req.params;
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 删除数据库中的记录
      await connection.execute('DELETE FROM xiaohongshu_uids WHERE file_name = ?', [fileName]);

      // 删除物理文件（如果存在）
      const uploadDir = path.join(__dirname, '../uploads/uids');
      const files = fs.readdirSync(uploadDir);
      const targetFile = files.find(file => file.includes(fileName));
      if (targetFile) {
        fs.unlinkSync(path.join(uploadDir, targetFile));
      }

      res.json({
        success: true,
        message: '文件删除成功'
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('删除UID文件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败: ' + error.message
    });
  }
});

// 重置UID使用状态
router.put('/uids/:id/reset', async (req, res) => {
  try {
    const { id } = req.params;
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.execute(`
        UPDATE xiaohongshu_uids
        SET is_used = 0, used_time = NULL, used_device_id = NULL
        WHERE id = ?
      `, [id]);

      res.json({
        success: true,
        message: 'UID状态重置成功'
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('重置UID状态失败:', error);
    res.status(500).json({
      success: false,
      message: '重置失败: ' + error.message
    });
  }
});

// 清空所有UID使用记录
router.put('/uids/clear-used-status', async (req, res) => {
  try {
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.execute(`
        UPDATE xiaohongshu_uids
        SET is_used = 0, used_time = NULL, used_device_id = NULL
      `);

      res.json({
        success: true,
        message: '所有UID使用记录已清空'
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('清空UID使用记录失败:', error);
    res.status(500).json({
      success: false,
      message: '清空失败: ' + error.message
    });
  }
});

// 获取可用的UID列表（用于脚本执行）
router.post('/get-available-uids', async (req, res) => {
  try {
    const { strategy = 'sequential', maxCount = 10, deviceId } = req.body;
    const connection = await mysql.createConnection(dbConfig);

    try {
      let query = '';
      let params = [];

      switch (strategy) {
        case 'unused':
          query = 'SELECT uid FROM xiaohongshu_uids WHERE is_used = 0 ORDER BY upload_time ASC LIMIT ?';
          params = [maxCount];
          break;
        case 'random':
          query = 'SELECT uid FROM xiaohongshu_uids ORDER BY RAND() LIMIT ?';
          params = [maxCount];
          break;
        case 'sequential':
        default:
          query = 'SELECT uid FROM xiaohongshu_uids ORDER BY upload_time ASC LIMIT ?';
          params = [maxCount];
          break;
      }

      const [rows] = await connection.execute(query, params);
      const uids = rows.map(row => row.uid);

      // 标记这些UID为已使用（如果需要）
      if (uids.length > 0 && deviceId) {
        const updateQuery = `
          UPDATE xiaohongshu_uids
          SET is_used = 1, used_time = NOW(), used_device_id = ?
          WHERE uid IN (${uids.map(() => '?').join(',')})
        `;
        await connection.execute(updateQuery, [deviceId, ...uids]);
      }

      res.json({
        success: true,
        data: uids
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('获取可用UID失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 记录UID私信结果
router.post('/record-uid-message', async (req, res) => {
  try {
    const { taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId } = req.body;
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 根据taskId判断功能类型
      let tableName;
      let insertQuery;
      let insertParams;

      // 检查taskId中是否包含uidFileMessage，如果包含则写入文件上传表，否则写入手动输入表
      if (taskId && taskId.includes('uidFileMessage')) {
        tableName = 'xiaohongshu_file_uid_messages';
        insertQuery = `
          INSERT INTO ${tableName}
          (task_id, device_id, device_name, uid, message_content, send_status, error_message, file_id, send_time)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        `;
        insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId || null];
      } else {
        tableName = 'xiaohongshu_manual_uid_messages';
        insertQuery = `
          INSERT INTO ${tableName}
          (task_id, device_id, device_name, uid, message_content, send_status, error_message, send_time)
          VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `;
        insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage];
      }

      await connection.execute(insertQuery, insertParams);

      console.log(`UID私信记录已保存到 ${tableName}: 设备${deviceId}, UID${uid}, 状态${sendStatus}`);

      res.json({
        success: true,
        message: '私信记录保存成功',
        tableName: tableName
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('记录UID私信结果失败:', error);
    res.status(500).json({
      success: false,
      message: '记录失败: ' + error.message
    });
  }
});

// 接收脚本执行完成通知
router.post('/execution-complete', async (req, res) => {
  try {
    const { taskId, deviceId, deviceName, totalCount, successCount, failedCount, executionLogs, completedAt } = req.body;

    console.log('收到UID私信执行完成通知:', {
      taskId,
      deviceId,
      successCount,
      failedCount
    });

    // 这里可以更新执行日志表的状态
    // 通知前端页面执行完成
    if (req.io) {
      req.io.emit('xiaohongshu-execution-complete', {
        taskId,
        deviceId,
        deviceName,
        totalCount,
        successCount,
        failedCount,
        executionLogs,
        completedAt
      });
    }

    res.json({
      success: true,
      message: '执行结果接收成功'
    });

  } catch (error) {
    console.error('处理执行完成通知失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});

// UID文件管理相关路由
const multer = require('multer');

// 配置文件上传
const uidStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/uid-files');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'uid-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const uidUpload = multer({ 
  storage: uidStorage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.txt', '.csv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 .txt 和 .csv 格式的文件'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 上传UID文件
router.post('/upload-uid-file', uidUpload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: '没有上传文件' });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;
    // 修复文件名编码问题
    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
    const fileSize = req.file.size;
    const uploadedBy = req.user ? req.user.username : 'admin';

    // 读取文件内容并解析UID
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n').filter(line => line.trim());
    const uids = lines.map(line => line.trim()).filter(uid => uid.length > 0);

    if (uids.length === 0) {
      fs.unlinkSync(filePath); // 删除空文件
      return res.status(400).json({ success: false, message: '文件中没有有效的UID' });
    }

    const connection = await db.pool.getConnection();

    try {
      // 插入文件记录
      const [fileResult] = await connection.execute(
        'INSERT INTO uid_files (file_name, original_name, file_path, file_size, total_uid_count, uploaded_by) VALUES (?, ?, ?, ?, ?, ?)',
        [fileName, originalName, filePath, fileSize, uids.length, uploadedBy]
      );

      const fileId = fileResult.insertId;

      // 批量插入UID数据
      const uidValues = uids.map(uid => [fileId, uid]);
      await connection.query(
        'INSERT INTO uid_data (file_id, uid) VALUES ?',
        [uidValues]
      );

      connection.release();

      res.json({
        success: true,
        message: 'UID文件上传成功',
        data: {
          fileId: fileId,
          fileName: originalName,
          totalCount: uids.length,
          uploadTime: new Date().toISOString()
        }
      });

    } catch (dbError) {
      connection.release();
      fs.unlinkSync(filePath); // 删除文件
      throw dbError;
    }

  } catch (error) {
    console.error('上传UID文件失败:', error);
    res.status(500).json({ success: false, message: '上传失败: ' + error.message });
  }
});

// 获取UID文件列表
router.get('/uid-files', async (req, res) => {
  try {
    const connection = await db.pool.getConnection();
    
    const [files] = await connection.execute(`
      SELECT 
        f.id,
        f.file_name,
        f.original_name,
        f.file_size,
        f.total_uid_count,
        f.uploaded_by,
        f.upload_time,
        f.status,
        COUNT(CASE WHEN d.is_used = 1 THEN 1 END) as used_count,
        COUNT(CASE WHEN d.is_used = 0 THEN 1 END) as unused_count
      FROM uid_files f
      LEFT JOIN uid_data d ON f.id = d.file_id
      WHERE f.status = 'active'
      GROUP BY f.id
      ORDER BY f.upload_time DESC
    `);

    connection.release();

    res.json({
      success: true,
      data: files
    });

  } catch (error) {
    console.error('获取UID文件列表失败:', error);
    res.status(500).json({ success: false, message: '获取文件列表失败' });
  }
});

// 获取指定文件的UID列表
router.get('/uid-files/:fileId/uids', async (req, res) => {
  try {
    const { fileId } = req.params;
    const { page = 1, limit = 50, status } = req.query;

    const connection = await db.pool.getConnection();
    
    let whereClause = 'WHERE file_id = ?';
    let params = [fileId];

    if (status === 'used') {
      whereClause += ' AND is_used = 1';
    } else if (status === 'unused') {
      whereClause += ' AND is_used = 0';
    }

    const offset = (page - 1) * limit;
    
    const [uids] = await connection.execute(`
      SELECT 
        id, uid, is_used, used_time, used_device_id, used_device_name, task_id, created_at
      FROM uid_data 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    // 获取总数
    const [countResult] = await connection.execute(`
      SELECT COUNT(*) as total
      FROM uid_data 
      ${whereClause}
    `, params);

    connection.release();

    res.json({
      success: true,
      data: {
        uids: uids,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: countResult[0].total,
          totalPages: Math.ceil(countResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取UID列表失败:', error);
    res.status(500).json({ success: false, message: '获取UID列表失败' });
  }
});

// 删除UID文件
router.delete('/uid-files/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params;

    const connection = await db.pool.getConnection();
    
    // 获取文件信息
    const [files] = await connection.execute(
      'SELECT file_path FROM uid_files WHERE id = ? AND status = "active"',
      [fileId]
    );

    if (files.length === 0) {
      connection.release();
      return res.status(404).json({ success: false, message: '文件不存在' });
    }

    const filePath = files[0].file_path;

    // 软删除文件记录
    await connection.execute(
      'UPDATE uid_files SET status = "deleted" WHERE id = ?',
      [fileId]
    );

    connection.release();

    // 删除物理文件
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    res.json({
      success: true,
      message: '文件删除成功'
    });

  } catch (error) {
    console.error('删除UID文件失败:', error);
    res.status(500).json({ success: false, message: '删除文件失败' });
  }
});

// 重置UID使用状态
router.post('/uid-files/:fileId/reset-status', async (req, res) => {
  try {
    const { fileId } = req.params;
    const { uidIds } = req.body; // 可选的UID ID列表，如果不提供则重置整个文件

    const connection = await db.pool.getConnection();
    
    if (uidIds && uidIds.length > 0) {
      // 重置指定的UID
      const placeholders = uidIds.map(() => '?').join(',');
      await connection.execute(
        `UPDATE uid_data SET is_used = 0, used_time = NULL, used_device_id = NULL, used_device_name = NULL, task_id = NULL WHERE file_id = ? AND id IN (${placeholders})`,
        [fileId, ...uidIds]
      );
    } else {
      // 重置整个文件的所有UID
      await connection.execute(
        'UPDATE uid_data SET is_used = 0, used_time = NULL, used_device_id = NULL, used_device_name = NULL, task_id = NULL WHERE file_id = ?',
        [fileId]
      );
    }

    connection.release();

    res.json({
      success: true,
      message: 'UID状态重置成功'
    });

  } catch (error) {
    console.error('重置UID状态失败:', error);
    res.status(500).json({ success: false, message: '重置状态失败' });
  }
});

// 获取可用的UID数量（用于设备分配）
router.get('/uid-files/available-count', async (req, res) => {
  try {
    const { fileId, count } = req.query;

    const connection = await db.pool.getConnection();
    
    if (fileId) {
      // 获取指定文件的可用UID数量
      const [result] = await connection.execute(
        'SELECT COUNT(*) as count FROM uid_data WHERE file_id = ? AND is_used = 0',
        [fileId]
      );
      
      connection.release();
      
      res.json({
        success: true,
        data: {
          availableCount: result[0].count,
          requestedCount: parseInt(count) || 0
        }
      });
    } else {
      // 获取所有文件的可用UID数量
      const [result] = await connection.execute(
        'SELECT COUNT(*) as count FROM uid_data WHERE is_used = 0'
      );
      
      connection.release();
      
      res.json({
        success: true,
        data: {
          availableCount: result[0].count,
          requestedCount: parseInt(count) || 0
        }
      });
    }

  } catch (error) {
    console.error('获取可用UID数量失败:', error);
    res.status(500).json({ success: false, message: '获取可用UID数量失败' });
  }
});

// 分配UID给设备
router.post('/uid-files/allocate-uids', async (req, res) => {
  try {
    const { fileId, deviceCount, totalCount, taskId } = req.body;

    if (!fileId || !deviceCount || !totalCount || !taskId) {
      return res.status(400).json({ success: false, message: '缺少必要参数' });
    }

    const connection = await db.pool.getConnection();
    
    try {
      // 检查可用UID数量
      const [availableResult] = await connection.execute(
        'SELECT COUNT(*) as count FROM uid_data WHERE file_id = ? AND is_used = 0',
        [fileId]
      );

      const availableCount = availableResult[0].count;
      
      if (availableCount < totalCount) {
        connection.release();
        return res.status(400).json({ 
          success: false, 
          message: `可用UID数量不足。需要: ${totalCount}, 可用: ${availableCount}` 
        });
      }

      // 获取可用的UID
      const [uids] = await connection.execute(
        'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 LIMIT ?',
        [fileId, totalCount]
      );

      // 按设备数量分配UID
      const deviceAllocations = [];
      const uidsPerDevice = Math.ceil(totalCount / deviceCount);
      
      for (let i = 0; i < deviceCount; i++) {
        const startIndex = i * uidsPerDevice;
        const endIndex = Math.min(startIndex + uidsPerDevice, uids.length);
        const deviceUids = uids.slice(startIndex, endIndex);
        
        if (deviceUids.length > 0) {
          deviceAllocations.push({
            deviceIndex: i,
            uids: deviceUids.map(u => u.uid),
            uidIds: deviceUids.map(u => u.id)
          });
        }
      }

      // 标记UID为已使用
      const uidIds = uids.map(u => u.id);
      if (uidIds.length > 0) {
        const placeholders = uidIds.map(() => '?').join(',');
        await connection.execute(
          `UPDATE uid_data SET is_used = 1, used_time = NOW(), task_id = ? WHERE id IN (${placeholders})`,
          [taskId, ...uidIds]
        );
      }

      connection.release();

      res.json({
        success: true,
        message: 'UID分配成功',
        data: {
          totalAllocated: uids.length,
          deviceAllocations: deviceAllocations
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('分配UID失败:', error);
    res.status(500).json({ success: false, message: '分配UID失败' });
  }
});

// ==================== 小红书视频发布功能 API ====================

// 配置视频文件上传
const videoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads/videos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'video-' + uniqueSuffix + ext);
  }
});

const videoUpload = multer({
  storage: videoStorage,
  fileFilter: function (req, file, cb) {
    // 支持常见的视频格式
    const allowedTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持视频格式文件: ' + allowedTypes.join(', ')));
    }
  },
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB限制
  }
});

// 上传视频文件（支持批量上传）
router.post('/upload-video-files', videoUpload.array('videos', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ success: false, message: '没有上传视频文件' });
    }

    const uploadedBy = req.user ? req.user.username : 'admin';
    const uploadResults = [];
    const connection = await db.pool.getConnection();

    try {
      for (const file of req.files) {
        const filePath = file.path;
        const fileName = file.filename;
        const originalName = file.originalname;
        const fileSize = file.size;
        const videoFormat = path.extname(originalName).toLowerCase().substring(1);

        // 插入视频文件记录
        const [fileResult] = await connection.execute(
          `INSERT INTO xiaohongshu_video_files
           (file_name, original_name, file_path, file_size, video_format, uploaded_by, description, tags)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [fileName, originalName, filePath, fileSize, videoFormat, uploadedBy, req.body.description || '', req.body.tags || '']
        );

        uploadResults.push({
          videoId: fileResult.insertId,
          fileName: originalName,
          fileSize: fileSize,
          videoFormat: videoFormat,
          uploadTime: new Date().toISOString()
        });
      }

      connection.release();

      res.json({
        success: true,
        message: `成功上传 ${req.files.length} 个视频文件`,
        data: {
          uploadCount: req.files.length,
          videos: uploadResults
        }
      });

    } catch (dbError) {
      connection.release();
      // 删除已上传的文件
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
      throw dbError;
    }

  } catch (error) {
    console.error('上传视频文件失败:', error);
    res.status(500).json({
      success: false,
      message: '上传视频文件失败: ' + error.message
    });
  }
});

// 获取已上传的视频文件列表
router.get('/video-files', async (req, res) => {
  try {
    const { page = 1, limit = 20, status = 'active' } = req.query;
    const offset = (page - 1) * limit;

    const connection = await db.pool.getConnection();

    try {
      // 获取视频文件列表
      const [videos] = await connection.execute(
        `SELECT
          vf.id,
          vf.file_name,
          vf.original_name,
          vf.file_size,
          vf.video_duration,
          vf.video_format,
          vf.video_resolution,
          vf.thumbnail_path,
          vf.uploaded_by,
          vf.upload_time,
          vf.description,
          vf.tags,
          COUNT(va.id) as assignment_count,
          COUNT(CASE WHEN va.status = 'completed' THEN 1 END) as completed_count
        FROM xiaohongshu_video_files vf
        LEFT JOIN xiaohongshu_video_assignments va ON vf.id = va.video_id
        WHERE vf.status = ?
        GROUP BY vf.id
        ORDER BY vf.upload_time DESC
        LIMIT ? OFFSET ?`,
        [status, parseInt(limit), parseInt(offset)]
      );

      // 获取总数
      const [countResult] = await connection.execute(
        'SELECT COUNT(*) as total FROM xiaohongshu_video_files WHERE status = ?',
        [status]
      );

      connection.release();

      res.json({
        success: true,
        data: {
          videos: videos,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            totalPages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('获取视频文件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取视频文件列表失败: ' + error.message
    });
  }
});

// 删除视频文件
router.delete('/video-files/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const connection = await db.pool.getConnection();

    try {
      // 获取视频文件信息
      const [videos] = await connection.execute(
        'SELECT file_path FROM xiaohongshu_video_files WHERE id = ? AND status = "active"',
        [videoId]
      );

      if (videos.length === 0) {
        connection.release();
        return res.status(404).json({ success: false, message: '视频文件不存在' });
      }

      // 软删除视频文件记录
      await connection.execute(
        'UPDATE xiaohongshu_video_files SET status = "deleted" WHERE id = ?',
        [videoId]
      );

      // 删除物理文件
      const filePath = videos[0].file_path;
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      connection.release();

      res.json({
        success: true,
        message: '视频文件删除成功'
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('删除视频文件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除视频文件失败: ' + error.message
    });
  }
});

// 分配视频给设备
router.post('/assign-videos', async (req, res) => {
  try {
    const { videoIds, deviceIds, taskId } = req.body;

    if (!videoIds || !deviceIds || !taskId || videoIds.length === 0 || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：videoIds, deviceIds, taskId'
      });
    }

    const connection = await db.pool.getConnection();

    try {
      // 获取设备信息
      const devicePlaceholders = deviceIds.map(() => '?').join(',');
      const [devices] = await connection.execute(
        `SELECT device_id, device_name FROM devices WHERE device_id IN (${devicePlaceholders})`,
        deviceIds
      );

      if (devices.length !== deviceIds.length) {
        connection.release();
        return res.status(400).json({
          success: false,
          message: '部分设备不存在或离线'
        });
      }

      // 验证视频文件存在
      const videoPlaceholders = videoIds.map(() => '?').join(',');
      const [videos] = await connection.execute(
        `SELECT id, original_name FROM xiaohongshu_video_files WHERE id IN (${videoPlaceholders}) AND status = 'active'`,
        videoIds
      );

      if (videos.length !== videoIds.length) {
        connection.release();
        return res.status(400).json({
          success: false,
          message: '部分视频文件不存在'
        });
      }

      // 平均分配视频给设备
      const assignments = [];
      const videosPerDevice = Math.ceil(videoIds.length / deviceIds.length);

      for (let i = 0; i < deviceIds.length; i++) {
        const device = devices[i];
        const startIndex = i * videosPerDevice;
        const endIndex = Math.min(startIndex + videosPerDevice, videoIds.length);
        const assignedVideoIds = videoIds.slice(startIndex, endIndex);

        for (const videoId of assignedVideoIds) {
          assignments.push([
            videoId,
            device.device_id,
            device.device_name,
            taskId
          ]);
        }
      }

      // 批量插入分配记录
      if (assignments.length > 0) {
        await connection.query(
          `INSERT INTO xiaohongshu_video_assignments
           (video_id, device_id, device_name, task_id) VALUES ?`,
          [assignments]
        );
      }

      connection.release();

      res.json({
        success: true,
        message: '视频分配成功',
        data: {
          totalVideos: videoIds.length,
          totalDevices: deviceIds.length,
          assignments: assignments.length
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('分配视频失败:', error);
    res.status(500).json({
      success: false,
      message: '分配视频失败: ' + error.message
    });
  }
});

// 获取设备的视频分配情况
router.get('/device-video-assignments/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { taskId } = req.query;

    const connection = await db.pool.getConnection();

    try {
      let query = `
        SELECT
          va.id as assignment_id,
          va.video_id,
          va.status,
          va.upload_progress,
          va.assignment_time,
          va.completed_time,
          va.video_title,
          va.video_description,
          va.error_message,
          vf.original_name,
          vf.file_size,
          vf.video_format,
          vf.video_duration,
          vf.description as video_description_original,
          vf.tags
        FROM xiaohongshu_video_assignments va
        JOIN xiaohongshu_video_files vf ON va.video_id = vf.id
        WHERE va.device_id = ?
      `;

      const params = [deviceId];

      if (taskId) {
        query += ' AND va.task_id = ?';
        params.push(taskId);
      }

      query += ' ORDER BY va.assignment_time DESC';

      const [assignments] = await connection.execute(query, params);

      connection.release();

      res.json({
        success: true,
        data: {
          deviceId: deviceId,
          assignments: assignments
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('获取设备视频分配失败:', error);
    res.status(500).json({
      success: false,
      message: '获取设备视频分配失败: ' + error.message
    });
  }
});

// 验证脚本执行状态
router.post('/verify-execution-status', async (req, res) => {
  try {
    const { taskId, deviceId, functionType } = req.body

    console.log('[验证执行状态] 收到验证请求:', { taskId, deviceId, functionType })

    // 检查设备是否在线
    const device = global.connectedDevices.get(deviceId)
    if (!device) {
      console.log('[验证执行状态] 设备不在线:', deviceId)
      return res.json({
        success: true,
        isRunning: false,
        reason: 'device_offline'
      })
    }

    // 检查是否有正在执行的任务
    const isRunning = global.executingTasks && global.executingTasks.has(taskId)

    console.log('[验证执行状态] 验证结果:', {
      taskId,
      deviceId,
      isRunning,
      executingTasks: global.executingTasks ? Array.from(global.executingTasks.keys()) : []
    })

    res.json({
      success: true,
      isRunning,
      taskId,
      deviceId,
      functionType
    })

  } catch (error) {
    console.error('[验证执行状态] 错误:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})

module.exports = router;
