/**
 * 管理员用户管理模块
 * 提供用户查看、统计、管理、权限管理等功能
 */

const fs = require('fs');

// 管理员用户管理模块设置函数
async function setupAdminUserManagement(app, io, coreData, authData) {
  console.log('🔧 设置管理员用户管理模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 引入认证服务和数据库连接
  const { localPool, mainPool } = require('../config/database');
  const AuthService = require('../services/AuthService');
  
  // 创建认证服务实例
  const authService = new AuthService(mainPool, localPool);

  // 管理员权限验证中间件
  const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }
    next();
  };

  // 获取用户列表API
  app.get('/api/admin/users', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        status = '',
        role = '',
        search = '',
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = req.query;

      let whereConditions = [];
      let params = [];

      if (status) {
        whereConditions.push('account_status = ?');
        params.push(status);
      }

      if (role) {
        whereConditions.push('role = ?');
        params.push(role);
      }

      if (search) {
        whereConditions.push('(username LIKE ? OR email LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? 
        `WHERE ${whereConditions.join(' AND ')}` : '';

      const validSortColumns = ['username', 'email', 'created_at', 'last_login_time', 'expires_at'];
      const finalSortBy = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
      const finalSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

      const offset = (page - 1) * limit;

      // 获取总数
      const [countResult] = await localPool.execute(`
        SELECT COUNT(*) as total FROM users ${whereClause}
      `, params);

      // 获取用户列表
      const [users] = await localPool.execute(`
        SELECT 
          id, username, email, role, account_status, is_main_verified,
          expires_at, last_activation_at, activation_count, total_duration_days,
          created_at, last_login_time, login_count, is_active, main_user_id
        FROM users 
        ${whereClause}
        ORDER BY ${finalSortBy} ${finalSortOrder}
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), parseInt(offset)]);

      // 为每个用户获取设备和执行统计
      const usersWithStats = await Promise.all(users.map(async (user) => {
        // 获取设备统计
        const [deviceStats] = await localPool.execute(`
          SELECT 
            COUNT(*) as total_devices,
            COUNT(CASE WHEN status = 'online' THEN 1 END) as online_devices,
            COUNT(CASE WHEN status = 'busy' THEN 1 END) as busy_devices,
            MAX(last_seen) as last_device_activity
          FROM devices WHERE user_id = ?
        `, [user.id]);

        // 获取执行统计
        const [execStats] = await localPool.execute(`
          SELECT 
            (SELECT COUNT(*) FROM xiaohongshu_execution_logs WHERE user_id = ?) as xiaohongshu_executions,
            (SELECT COUNT(*) FROM xianyu_execution_logs WHERE user_id = ?) as xianyu_executions
        `, [user.id, user.id]);

        // 获取文件统计
        const [fileStats] = await localPool.execute(`
          SELECT 
            COUNT(*) as total_files,
            SUM(file_size) as total_file_size
          FROM uid_files WHERE user_id = ?
        `, [user.id]);

        const now = new Date();
        const expiresAt = user.expires_at ? new Date(user.expires_at) : null;
        const daysRemaining = expiresAt ? Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24)) : 0;

        return {
          ...user,
          isExpired: expiresAt ? now > expiresAt : false,
          daysRemaining: Math.max(0, daysRemaining),
          deviceStats: deviceStats[0],
          execStats: {
            xiaohongshu_executions: execStats[0].xiaohongshu_executions || 0,
            xianyu_executions: execStats[0].xianyu_executions || 0,
            total_executions: (execStats[0].xiaohongshu_executions || 0) + (execStats[0].xianyu_executions || 0)
          },
          fileStats: fileStats[0]
        };
      }));

      res.json({
        success: true,
        data: {
          users: usersWithStats,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            pages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户统计信息API
  app.get('/api/admin/users/stats', authenticateToken, requireAdmin, async (req, res) => {
    try {
      // 获取基础用户统计
      const [userStats] = await localPool.execute(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN account_status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN account_status = 'expired' THEN 1 END) as expired_users,
          COUNT(CASE WHEN account_status = 'disabled' THEN 1 END) as disabled_users,
          COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at <= NOW() THEN 1 END) as time_expired_users,
          COUNT(CASE WHEN is_main_verified = 1 THEN 1 END) as main_verified_users,
          COUNT(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_last_week,
          COUNT(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_last_month,
          AVG(login_count) as avg_login_count,
          AVG(total_duration_days) as avg_total_duration
        FROM users
      `);

      // 获取设备统计
      const [deviceStats] = await localPool.execute(`
        SELECT 
          COUNT(*) as total_devices,
          COUNT(CASE WHEN status = 'online' THEN 1 END) as online_devices,
          COUNT(CASE WHEN status = 'busy' THEN 1 END) as busy_devices,
          COUNT(CASE WHEN status = 'offline' THEN 1 END) as offline_devices,
          COUNT(DISTINCT user_id) as users_with_devices
        FROM devices
      `);

      // 获取执行统计
      const [execStats] = await localPool.execute(`
        SELECT 
          (SELECT COUNT(*) FROM xiaohongshu_execution_logs) as total_xiaohongshu_executions,
          (SELECT COUNT(*) FROM xianyu_execution_logs) as total_xianyu_executions,
          (SELECT COUNT(DISTINCT user_id) FROM xiaohongshu_execution_logs) as xiaohongshu_users,
          (SELECT COUNT(DISTINCT user_id) FROM xianyu_execution_logs) as xianyu_users
      `);

      // 获取文件统计
      const [fileStats] = await localPool.execute(`
        SELECT 
          COUNT(*) as total_files,
          SUM(file_size) as total_file_size,
          COUNT(DISTINCT user_id) as users_with_files,
          AVG(file_size) as avg_file_size
        FROM uid_files
      `);

      // 获取角色统计
      const [roleStats] = await localPool.execute(`
        SELECT 
          role,
          COUNT(*) as count
        FROM users
        GROUP BY role
      `);

      // 获取最近注册用户
      const [recentUsers] = await localPool.execute(`
        SELECT username, email, created_at, account_status
        FROM users
        ORDER BY created_at DESC
        LIMIT 10
      `);

      res.json({
        success: true,
        data: {
          users: userStats[0],
          devices: deviceStats[0],
          executions: execStats[0],
          files: fileStats[0],
          roles: roleStats,
          recentUsers: recentUsers
        }
      });

    } catch (error) {
      console.error('获取用户统计失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户详细信息API
  app.get('/api/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;

      // 获取用户基本信息
      const [users] = await localPool.execute(`
        SELECT * FROM users WHERE id = ?
      `, [id]);

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const user = users[0];

      // 获取用户设备列表
      const [devices] = await localPool.execute(`
        SELECT device_id, device_name, device_info, status, last_seen, created_at
        FROM devices WHERE user_id = ?
        ORDER BY last_seen DESC
      `, [id]);

      // 获取用户激活记录
      const [activations] = await localPool.execute(`
        SELECT * FROM local_activations WHERE user_id = ?
        ORDER BY activated_at DESC
      `, [id]);

      // 获取用户执行日志（最近20条）
      const [execLogs] = await localPool.execute(`
        SELECT 'xiaohongshu' as platform, function_type, execution_status, started_at, completed_at, device_id
        FROM xiaohongshu_execution_logs WHERE user_id = ?
        UNION ALL
        SELECT 'xianyu' as platform, function_type, execution_status, started_at, completed_at, device_id
        FROM xianyu_execution_logs WHERE user_id = ?
        ORDER BY started_at DESC
        LIMIT 20
      `, [id, id]);

      // 获取用户文件列表（包括UID文件和视频文件）
      const [uidFiles] = await localPool.execute(`
        SELECT id, file_name as filename, file_size, upload_time, 'uid' as file_type
        FROM uid_files WHERE user_id = ?
        ORDER BY upload_time DESC
      `, [id]);

      const [videoFiles] = await localPool.execute(`
        SELECT id, file_name as filename, file_size, upload_time, 'video' as file_type
        FROM xiaohongshu_video_files WHERE user_id = ? AND status = 'active'
        ORDER BY upload_time DESC
      `, [id]);

      // 合并文件列表
      const files = [...uidFiles, ...videoFiles].sort((a, b) =>
        new Date(b.upload_time) - new Date(a.upload_time)
      );

      res.json({
        success: true,
        data: {
          user: user,
          devices: devices,
          activations: activations,
          recentExecutions: execLogs,
          recentFiles: files
        }
      });

    } catch (error) {
      console.error('获取用户详细信息失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 更新用户状态API
  app.put('/api/admin/users/:id/status', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!['active', 'expired', 'disabled'].includes(status)) {
        return res.status(400).json({
          success: false,
          message: '无效的状态值'
        });
      }

      await localPool.execute(
        'UPDATE users SET account_status = ? WHERE id = ?',
        [status, id]
      );

      console.log(`管理员更新用户状态: ID ${id} -> ${status}`);

      res.json({
        success: true,
        message: '用户状态更新成功'
      });

    } catch (error) {
      console.error('更新用户状态失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 更新用户角色API
  app.put('/api/admin/users/:id/role', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { role } = req.body;

      if (!['user', 'admin'].includes(role)) {
        return res.status(400).json({
          success: false,
          message: '无效的角色值'
        });
      }

      await localPool.execute(
        'UPDATE users SET role = ? WHERE id = ?',
        [role, id]
      );

      console.log(`管理员更新用户角色: ID ${id} -> ${role}`);

      res.json({
        success: true,
        message: '用户角色更新成功'
      });

    } catch (error) {
      console.error('更新用户角色失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 延长用户时效API
  app.put('/api/admin/users/:id/extend', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const { days } = req.body;

      if (!days || days < 1 || days > 3650) {
        return res.status(400).json({
          success: false,
          message: '延长天数必须在1-3650之间'
        });
      }

      // 获取当前用户信息
      const [users] = await localPool.execute(
        'SELECT expires_at, total_duration_days FROM users WHERE id = ?',
        [id]
      );

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      const user = users[0];
      let newExpiresAt = new Date();
      
      if (user.expires_at && new Date(user.expires_at) > new Date()) {
        // 如果当前时效未过期，在现有基础上延长
        newExpiresAt = new Date(user.expires_at);
      }
      
      newExpiresAt.setDate(newExpiresAt.getDate() + parseInt(days));

      await localPool.execute(
        `UPDATE users SET 
         expires_at = ?, 
         total_duration_days = ?, 
         account_status = 'active',
         last_activation_at = NOW()
         WHERE id = ?`,
        [newExpiresAt, (user.total_duration_days || 0) + parseInt(days), id]
      );

      // 记录管理员操作
      await localPool.execute(
        `INSERT INTO local_activations 
         (user_id, activation_code, activation_type, duration_days, expires_at, notes)
         VALUES (?, 'ADMIN_EXTEND', 'renewal', ?, ?, '管理员延长时效')`,
        [id, days, newExpiresAt]
      );

      console.log(`管理员延长用户时效: ID ${id}, 延长${days}天`);

      res.json({
        success: true,
        message: `用户时效延长${days}天成功`,
        data: {
          newExpiresAt: newExpiresAt
        }
      });

    } catch (error) {
      console.error('延长用户时效失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 删除用户API
  app.delete('/api/admin/users/:id', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;

      // 检查是否为管理员账号
      const [users] = await localPool.execute(
        'SELECT role FROM users WHERE id = ?',
        [id]
      );

      if (users.length === 0) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      if (users[0].role === 'admin') {
        return res.status(400).json({
          success: false,
          message: '不能删除管理员账号'
        });
      }

      // 删除用户相关数据（级联删除）
      const connection = await localPool.getConnection();
      
      try {
        await connection.beginTransaction();

        // 删除用户的设备
        await connection.execute('DELETE FROM devices WHERE user_id = ?', [id]);
        
        // 删除用户的执行日志
        await connection.execute('DELETE FROM xiaohongshu_execution_logs WHERE user_id = ?', [id]);
        await connection.execute('DELETE FROM xianyu_execution_logs WHERE user_id = ?', [id]);
        
        // 删除用户的文件
        await connection.execute('DELETE FROM uid_files WHERE user_id = ?', [id]);
        await connection.execute('DELETE FROM uid_data WHERE user_id = ?', [id]);
        
        // 删除用户的激活记录
        await connection.execute('DELETE FROM local_activations WHERE user_id = ?', [id]);
        
        // 删除用户
        await connection.execute('DELETE FROM users WHERE id = ?', [id]);

        await connection.commit();
        
        console.log(`管理员删除用户: ID ${id}`);

        res.json({
          success: true,
          message: '用户删除成功'
        });

      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }

    } catch (error) {
      console.error('删除用户失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 删除用户文件API
  app.delete('/api/admin/users/:userId/files/:fileId', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId, fileId } = req.params;
      const { fileType } = req.query; // 'uid' 或 'video'

      if (!fileType || !['uid', 'video'].includes(fileType)) {
        return res.status(400).json({
          success: false,
          message: '无效的文件类型'
        });
      }

      let tableName = fileType === 'uid' ? 'uid_files' : 'xiaohongshu_video_files';

      // 先获取文件信息（包括文件路径）
      const [fileInfo] = await localPool.execute(
        `SELECT file_path, original_name FROM ${tableName} WHERE id = ? AND user_id = ?`,
        [fileId, userId]
      );

      if (fileInfo.length === 0) {
        return res.status(404).json({
          success: false,
          message: '文件不存在或无权删除'
        });
      }

      const filePath = fileInfo[0].file_path;
      const originalName = fileInfo[0].original_name;

      // 删除数据库记录
      const [result] = await localPool.execute(
        `DELETE FROM ${tableName} WHERE id = ? AND user_id = ?`,
        [fileId, userId]
      );

      // 删除物理文件
      if (filePath && fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log(`物理文件已删除: ${filePath}`);
        } catch (fileError) {
          console.error('删除物理文件失败:', fileError);
          // 不抛出错误，因为数据库记录已经删除成功
        }
      }

      // 如果是UID文件，还需要删除相关的UID数据
      if (fileType === 'uid') {
        try {
          await localPool.execute('DELETE FROM uid_data WHERE file_id = ?', [fileId]);
          console.log(`UID数据已删除: 文件ID ${fileId}`);
        } catch (uidError) {
          console.error('删除UID数据失败:', uidError);
        }
      }

      console.log(`管理员删除用户文件: 用户ID ${userId}, 文件ID ${fileId}, 类型 ${fileType}, 文件名 ${originalName}`);

      res.json({
        success: true,
        message: '文件删除成功'
      });

    } catch (error) {
      console.error('删除用户文件失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 清空用户执行记录API
  app.delete('/api/admin/users/:userId/execution-logs', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId } = req.params;
      const { platform } = req.query; // 'xiaohongshu', 'xianyu', 或 'all'

      if (platform === 'xiaohongshu' || platform === 'all') {
        await localPool.execute('DELETE FROM xiaohongshu_execution_logs WHERE user_id = ?', [userId]);
      }

      if (platform === 'xianyu' || platform === 'all') {
        await localPool.execute('DELETE FROM xianyu_execution_logs WHERE user_id = ?', [userId]);
      }

      console.log(`管理员清空用户执行记录: 用户ID ${userId}, 平台 ${platform || 'all'}`);

      res.json({
        success: true,
        message: '执行记录清空成功'
      });

    } catch (error) {
      console.error('清空用户执行记录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户设备列表（分页+搜索）
  app.get('/api/admin/users/:userId/devices', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10, search = '' } = req.query;

      const offset = (page - 1) * limit;

      let whereClause = 'WHERE user_id = ?';
      let params = [userId];

      if (search) {
        whereClause += ' AND (device_name LIKE ? OR device_id LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      // 获取总数
      const [countResult] = await localPool.execute(
        `SELECT COUNT(*) as total FROM devices ${whereClause}`,
        params
      );

      // 获取分页数据
      const [devices] = await localPool.execute(`
        SELECT device_id, device_name, device_info, status, last_seen, created_at
        FROM devices ${whereClause}
        ORDER BY last_seen DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), offset]);

      res.json({
        success: true,
        data: {
          devices,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            totalPages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取用户设备列表失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户激活记录（分页+搜索）
  app.get('/api/admin/users/:userId/activations', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10, search = '' } = req.query;

      const offset = (page - 1) * limit;

      let whereClause = 'WHERE user_id = ?';
      let params = [userId];

      if (search) {
        whereClause += ' AND (activation_code LIKE ? OR activation_type LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      // 获取总数
      const [countResult] = await localPool.execute(
        `SELECT COUNT(*) as total FROM local_activations ${whereClause}`,
        params
      );

      // 获取分页数据
      const [activations] = await localPool.execute(`
        SELECT * FROM local_activations ${whereClause}
        ORDER BY activated_at DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), offset]);

      res.json({
        success: true,
        data: {
          activations,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            totalPages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取用户激活记录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户执行记录（分页+搜索）
  app.get('/api/admin/users/:userId/executions', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10, search = '', platform = '' } = req.query;

      const offset = (page - 1) * limit;

      // 构建搜索条件
      let searchCondition = '';
      let searchParams = [];

      if (search) {
        searchCondition = ' AND (function_type LIKE ? OR device_id LIKE ? OR execution_status LIKE ?)';
        searchParams = [`%${search}%`, `%${search}%`, `%${search}%`];
      }

      let executions = [];
      let totalCount = 0;

      // 根据平台筛选获取数据
      if (platform === 'xiaohongshu') {
        // 只查询小红书数据
        const [countResult] = await localPool.execute(
          `SELECT COUNT(*) as total FROM xiaohongshu_execution_logs WHERE user_id = ?${searchCondition}`,
          [userId, ...searchParams]
        );
        totalCount = countResult[0].total;

        const [results] = await localPool.execute(`
          SELECT id, 'xiaohongshu' as platform, function_type, execution_status,
                 started_at, completed_at, device_id, progress_percentage, error_message
          FROM xiaohongshu_execution_logs
          WHERE user_id = ?${searchCondition}
          ORDER BY started_at DESC
          LIMIT ? OFFSET ?
        `, [userId, ...searchParams, parseInt(limit), offset]);

        executions = results;

      } else if (platform === 'xianyu') {
        // 只查询闲鱼数据
        const [countResult] = await localPool.execute(
          `SELECT COUNT(*) as total FROM xianyu_execution_logs WHERE user_id = ?${searchCondition}`,
          [userId, ...searchParams]
        );
        totalCount = countResult[0].total;

        const [results] = await localPool.execute(`
          SELECT id, 'xianyu' as platform, function_type, execution_status,
                 started_at, completed_at, device_id, progress_percentage, error_message
          FROM xianyu_execution_logs
          WHERE user_id = ?${searchCondition}
          ORDER BY started_at DESC
          LIMIT ? OFFSET ?
        `, [userId, ...searchParams, parseInt(limit), offset]);

        executions = results;

      } else {
        // 查询所有平台数据
        const [xiaohongShuCount] = await localPool.execute(
          `SELECT COUNT(*) as count FROM xiaohongshu_execution_logs WHERE user_id = ?${searchCondition}`,
          [userId, ...searchParams]
        );

        const [xianyuCount] = await localPool.execute(
          `SELECT COUNT(*) as count FROM xianyu_execution_logs WHERE user_id = ?${searchCondition}`,
          [userId, ...searchParams]
        );

        totalCount = xiaohongShuCount[0].count + xianyuCount[0].count;

        // 使用UNION查询并分页
        const [results] = await localPool.execute(`
          (SELECT id, 'xiaohongshu' as platform, function_type, execution_status,
                  started_at, completed_at, device_id, progress_percentage, error_message
           FROM xiaohongshu_execution_logs
           WHERE user_id = ?${searchCondition})
          UNION ALL
          (SELECT id, 'xianyu' as platform, function_type, execution_status,
                  started_at, completed_at, device_id, progress_percentage, error_message
           FROM xianyu_execution_logs
           WHERE user_id = ?${searchCondition})
          ORDER BY started_at DESC
          LIMIT ? OFFSET ?
        `, [userId, ...searchParams, userId, ...searchParams, parseInt(limit), offset]);

        executions = results;
      }

      res.json({
        success: true,
        data: {
          executions,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: totalCount,
            totalPages: Math.ceil(totalCount / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取用户执行记录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 获取用户文件列表（分页+搜索）
  app.get('/api/admin/users/:userId/files', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId } = req.params;
      const { page = 1, limit = 10, search = '', fileType = '' } = req.query;

      const offset = (page - 1) * limit;

      let whereConditions = [];
      let params = [];

      if (search) {
        whereConditions.push('file_name LIKE ?');
        params.push(`%${search}%`);
      }

      let unionQuery = '';
      let countQuery = '';

      if (fileType === 'uid' || fileType === '') {
        const uidWhere = whereConditions.length > 0 ?
          `WHERE user_id = ? AND ${whereConditions.join(' AND ')}` :
          'WHERE user_id = ?';

        unionQuery += `
          SELECT id, file_name as filename, file_size, upload_time, 'uid' as file_type
          FROM uid_files ${uidWhere}
        `;

        countQuery += `
          SELECT COUNT(*) as count FROM uid_files ${uidWhere}
        `;
      }

      if (fileType === 'video' || fileType === '') {
        if (unionQuery) {
          unionQuery += ' UNION ALL ';
          countQuery += ' UNION ALL ';
        }

        const videoWhere = whereConditions.length > 0 ?
          `WHERE user_id = ? AND status = 'active' AND ${whereConditions.join(' AND ')}` :
          'WHERE user_id = ? AND status = \'active\'';

        unionQuery += `
          SELECT id, file_name as filename, file_size, upload_time, 'video' as file_type
          FROM xiaohongshu_video_files ${videoWhere}
        `;

        countQuery += `
          SELECT COUNT(*) as count FROM xiaohongshu_video_files ${videoWhere}
        `;
      }

      // 准备参数
      let queryParams = [];
      if (fileType === 'uid' || fileType === '') {
        queryParams.push(userId, ...params);
      }
      if (fileType === 'video' || fileType === '') {
        queryParams.push(userId, ...params);
      }

      // 获取总数
      const totalCountQuery = `SELECT SUM(count) as total FROM (${countQuery}) as counts`;
      const [countResult] = await localPool.execute(totalCountQuery, queryParams);

      // 获取分页数据
      const finalQuery = `${unionQuery} ORDER BY upload_time DESC LIMIT ? OFFSET ?`;
      const [files] = await localPool.execute(finalQuery, [...queryParams, parseInt(limit), offset]);

      res.json({
        success: true,
        data: {
          files,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total || 0,
            totalPages: Math.ceil((countResult[0].total || 0) / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取用户文件列表失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 删除单条执行记录
  app.delete('/api/admin/users/:userId/executions/:executionId', authenticateToken, requireAdmin, async (req, res) => {
    try {
      const { userId, executionId } = req.params;
      const { platform } = req.query; // 'xiaohongshu' 或 'xianyu'

      if (!platform || !['xiaohongshu', 'xianyu'].includes(platform)) {
        return res.status(400).json({
          success: false,
          message: '无效的平台参数'
        });
      }

      const tableName = platform === 'xiaohongshu' ? 'xiaohongshu_execution_logs' : 'xianyu_execution_logs';

      const [result] = await localPool.execute(
        `DELETE FROM ${tableName} WHERE id = ? AND user_id = ?`,
        [executionId, userId]
      );

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: '执行记录不存在或无权删除'
        });
      }

      console.log(`管理员删除用户执行记录: 用户ID ${userId}, 记录ID ${executionId}, 平台 ${platform}`);

      res.json({
        success: true,
        message: '执行记录删除成功'
      });

    } catch (error) {
      console.error('删除执行记录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  console.log('✅ 管理员用户管理模块设置完成');
}

module.exports = { setupAdminUserManagement };
