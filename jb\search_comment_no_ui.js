// 小红书搜索评论脚本（无UI版本）

// ==================== 小红书应用启动器 ====================
/**
 * 启动指定名称的小红书应用
 * @param {string} appName - 要启动的小红书应用名称
 * @returns {boolean} - 启动是否成功
 */
function launchXiaohongshuByName(appName) {
    if (!appName || appName.trim() === '') {
        addLog("未指定小红书应用名称，使用默认启动方式");
        return launchXiaohongshuDefault();
    }

    addLog("正在启动小红书应用: " + appName);

    // 先按home键回到桌面
    home();
    sleep(2000);
    home();
    sleep(1000);

    // 获取屏幕尺寸
    var screenWidth = device.width;
    var screenHeight = device.height;

    addLog("屏幕尺寸: " + screenWidth + " x " + screenHeight);

    // 滑动函数
    function swipeLeft() {
        addLog("执行左滑动作");
        swipe(screenWidth * 0.8, screenHeight * 0.5, screenWidth * 0.2, screenHeight * 0.5, 500);
        sleep(1000);
    }

    function swipeRight() {
        addLog("执行右滑动作");
        swipe(screenWidth * 0.2, screenHeight * 0.5, screenWidth * 0.8, screenHeight * 0.5, 500);
        sleep(1000);
    }

    // 查找元素函数
    function findTargetElement() {
        return className("android.widget.TextView").text(appName).findOne(2000);
    }

    try {
        addLog("开始查找目标应用: " + appName);

        var targetElement = findTargetElement();
        var maxAttempts = 10; // 最多尝试10次滑动（左5次，右5次）
        var attempts = 0;

        // 如果没找到元素，开始滑动查找
        while (!targetElement && attempts < maxAttempts) {
            attempts++;
            addLog("第" + attempts + "次尝试滑动查找...");

            if (attempts <= 5) {
                // 前5次向左滑动
                swipeLeft();
            } else {
                // 后5次向右滑动
                swipeRight();
            }

            // 滑动后重新查找
            targetElement = findTargetElement();
        }

        if (targetElement) {
            addLog("找到目标应用：" + targetElement.text());

            // 获取元素位置信息
            var bounds = targetElement.bounds();
            addLog("应用位置：x=" + bounds.centerX() + ", y=" + bounds.centerY());

            // 点击元素
            targetElement.click();
            addLog("已点击目标应用");

            // 等待应用启动
            sleep(3000);

            // 检查是否成功启动小红书
            var currentPkg = currentPackage();
            if (currentPkg && currentPkg.includes("xhs")) {
                addLog("✅ 小红书应用启动成功");
                return true;
            } else {
                addLog("⚠️ 应用已点击，但可能未成功启动小红书，当前应用: " + (currentPkg || "未知"));
                // 即使包名检查失败，也认为启动成功，因为可能是分身应用包名不同
                return true;
            }

        } else {
            addLog("❌ 经过" + attempts + "次滑动后仍未找到目标应用：" + appName);

            // 显示当前界面所有TextView元素用于调试
            addLog("当前界面的TextView元素：");
            var allTextViews = className("android.widget.TextView").find();
            for (var i = 0; i < allTextViews.length && i < 10; i++) {
                var text = allTextViews[i].text();
                if (text && text.trim() !== '') {
                    addLog("TextView " + i + ": " + text);
                }
            }

            // 如果找不到指定应用，尝试使用默认启动方式
            addLog("尝试使用默认启动方式");
            return launchXiaohongshuDefault();
        }

    } catch (error) {
        addLog("❌ 启动应用过程出错：" + error.message);
        addLog("尝试使用默认启动方式");
        return launchXiaohongshuDefault();
    }
}

/**
 * 默认的小红书启动方式（兼容旧版本）
 * @returns {boolean} - 启动是否成功
 */
function launchXiaohongshuDefault() {
    addLog("使用默认方式启动小红书...");

    try {
        // 先尝试通过应用名启动
        launchApp("小红书");
        sleep(3000);

        // 检查是否成功启动
        var currentPkg = currentPackage();
        if (currentPkg && currentPkg.includes("xhs")) {
            addLog("✅ 默认方式启动小红书成功");
            return true;
        }

        // 如果失败，尝试坐标点击方式
        addLog("尝试坐标点击方式启动");
        home();
        sleep(2000);

        var screenWidth = device.width;
        var screenHeight = device.height;
        var clickX = screenWidth * 0.5;
        var clickY = screenHeight * 0.65;

        click(clickX, clickY);
        sleep(3000);

        addLog("✅ 小红书启动完成（默认方式）");
        return true;

    } catch (error) {
        addLog("❌ 默认启动方式失败: " + error.message);
        return false;
    }
}

// 脚本配置参数
const CONFIG = {
    keyword: "美食推荐",        // 搜索关键词
    commentCount: 3,           // 评论文章数量
    delay: 5,                  // 操作间隔（秒）
    commentMode: "emoji",      // 评论模式：emoji（默认表情）或 text（文本评论）
    commentText: "很棒的分享！" // 文本评论内容
};

// 全局变量
let isRunning = false;
let commentedArticleElements = []; // 记录已评论过的文章控件信息
let foundArticleElements = []; // 记录找过的文章控件信息

// 实时状态统计变量
let commentedArticleCount = 0;  // 已评论文章数
let processedStepCount = 0;     // 已处理步骤数
let searchAttemptCount = 0;     // 搜索尝试次数

// 添加日志函数
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = "[" + timestamp + "] " + message;
    console.log(logMessage);
}

// 实时状态上报函数
function sendRealtimeStatus(statusData) {
    try {
        // 构造要发送的数据
        const data = {
            deviceId: "DEVICE_ID_PLACEHOLDER", // 将在脚本生成时替换
            taskId: "TASK_ID_PLACEHOLDER", // 将在脚本生成时替换
            commentedArticleCount: commentedArticleCount || 0,
            processedStepCount: processedStepCount || 0,
            searchAttemptCount: searchAttemptCount || 0,
            currentStatus: statusData.currentStatus || "",
            message: statusData.message || "",
            timestamp: new Date().toISOString()
        };

        // 手动合并statusData的属性（兼容旧版Auto.js）
        if (statusData) {
            for (let key in statusData) {
                if (statusData.hasOwnProperty(key)) {
                    data[key] = statusData[key];
                }
            }
        }

        // 使用线程发送HTTP请求到服务器
        threads.start(function() {
            try {
                const response = http.postJson("http://SERVER_HOST_PLACEHOLDER/api/xiaohongshu/realtime-status", data, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 3000
                });

                if (response && response.statusCode === 200) {
                    console.log("实时状态上报成功");
                } else {
                    console.log("实时状态上报失败: " + (response ? response.statusCode : "无响应"));
                }
            } catch (e) {
                console.log("实时状态上报网络错误: " + e.message);
            }
        });

        // 同时记录本地日志
        const logMessage = '实时状态: ' + JSON.stringify(statusData);
        addLog(logMessage);
    } catch (e) {
        // 静默处理错误
        console.log("实时状态上报错误: " + e.message);
    }
}

// 更新已评论文章数
function updateCommentedArticleCount() {
    commentedArticleCount++;
    sendRealtimeStatus({
        commentedArticleCount: commentedArticleCount,
        message: '已评论文章数: ' + commentedArticleCount
    });
}

// 更新已处理步骤数
function updateProcessedStepCount() {
    processedStepCount++;
    sendRealtimeStatus({
        processedStepCount: processedStepCount,
        message: '已处理步骤数: ' + processedStepCount
    });
}

// 更新搜索尝试次数
function updateSearchAttemptCount() {
    searchAttemptCount++;
    sendRealtimeStatus({
        searchAttemptCount: searchAttemptCount,
        message: '搜索尝试次数: ' + searchAttemptCount
    });
}

// 更新当前状态
function updateCurrentStatus(status) {
    addLog('状态更新: ' + status);
    sendRealtimeStatus({
        currentStatus: status,
        message: '状态更新: ' + status
    });
}

// 检查无障碍服务
function checkAccessibilityService() {
    if (!auto.service) {
        addLog("无障碍服务未开启，请先开启无障碍服务");
        // 移除UI相关调用，避免UI线程错误
        // app.startActivity({
        //     action: "android.settings.ACCESSIBILITY_SETTINGS"
        // });
        return false;
    }
    addLog("无障碍服务已开启");
    return true;
}

// 主函数
function main() {
    addLog("=== 小红书搜索评论脚本启动 ===");

    // 检查无障碍服务
    if (!checkAccessibilityService()) {
        return;
    }

    // 显示脚本配置
    addLog("搜索关键词: " + CONFIG.keyword);
    addLog("评论数量: " + CONFIG.commentCount);
    addLog("操作间隔: " + CONFIG.delay + "秒");

    // 开始执行脚本
    isRunning = true;
    try {
        executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    } catch (e) {
        addLog("脚本执行出错: " + e.message);
        console.error("脚本执行出错: " + e.message);

        // 脚本执行出错后也要关闭小红书应用
        addLog("脚本执行出错，休眠5秒后关闭小红书应用");
        sleep(5000);
        closeXiaohongshuApp();
    } finally {
        isRunning = false;
        addLog("=== 脚本执行结束 ===");
    }
}

// ==================== 核心功能函数 ====================

// 主执行函数
function executeSearchComment(keyword, commentCount, delay) {
    addLog("初始化脚本环境");
    updateCurrentStatus("初始化脚本环境");
    device.keepScreenOn();

    // 启动小红书
    addLog("正在启动小红书应用");
    updateCurrentStatus("正在启动小红书应用");
    updateProcessedStepCount();
    var selectedApp = (typeof CONFIG !== 'undefined' && CONFIG.selectedApp) ? CONFIG.selectedApp : '';
    if (!launchXiaohongshu(selectedApp)) {
        addLog("启动小红书失败");
        updateCurrentStatus("启动小红书失败");
        return;
    }

    sleep(5000);
    addLog("小红书启动成功");
    updateCurrentStatus("小红书启动成功");

    // 确保在主页并在首页页面
    addLog("确保在主页并在首页页面");
    updateCurrentStatus("确保在主页");
    updateProcessedStepCount();
    backToHomeAndEnsureHomePage();

    // 进行搜索
    addLog("开始搜索: " + keyword);
    updateCurrentStatus("开始搜索: " + keyword);
    updateProcessedStepCount();
    updateSearchAttemptCount();
    if (!performSearch(keyword)) {
        addLog("搜索失败");
        updateCurrentStatus("搜索失败");
        return;
    }

    sleep(3000);
    addLog("搜索完成，开始实时查找文章并评论");
    updateCurrentStatus("搜索完成，开始查找文章并评论");
    updateProcessedStepCount();

    // 清空已评论文章控件记录和找过的文章控件记录
    commentedArticleElements = [];
    foundArticleElements = [];
    addLog("清空已评论文章控件记录和找过的文章控件记录");
    updateCurrentStatus("准备开始评论文章");

    // 新的搜索后逻辑：实时查找和评论文章
    let successCount = 0;
    let attemptCount = 0;
    let consecutiveScrollCount = 0; // 连续滑动次数
    const maxAttempts = 100; // 增加最大尝试次数
    const maxConsecutiveScrolls = 10; // 最大连续滑动次数

    while (successCount < commentCount && isRunning && attemptCount < maxAttempts) {
        attemptCount++;
        addLog("第 " + attemptCount + " 次尝试，当前已评论: " + successCount + "/" + commentCount);
        updateCurrentStatus("第 " + attemptCount + " 次尝试，已评论: " + successCount + "/" + commentCount);
        updateSearchAttemptCount();

        // 检查是否在搜索结果页面
        if (!isInSearchResults()) {
            addLog("不在搜索结果页面，尝试返回");
            updateCurrentStatus("不在搜索结果页面，尝试返回");
            backToSearchResults();
            sleep(2000);
            continue;
        }

        // 查找当前页面的文章控件（已自动跳过已评论的文章）
        updateCurrentStatus("正在查找当前页面的文章");
        let articleElement = findCurrentPageArticle();
        if (articleElement) {
            // 找到新文章，重置连续滑动计数
            consecutiveScrollCount = 0;
            addLog("找到新文章控件: " + articleElement.text.substring(0, 30) + "...");
            updateCurrentStatus("找到新文章，正在点击进入");

            // 点击文章进入详情页
            if (clickArticleElement(articleElement)) {
                updateCurrentStatus("正在等待文章详情页加载");
                updateProcessedStepCount();
                sleep(3000);

                // 判断是否成功到达文章详情页面
                if (isInArticleDetailPage()) {
                    addLog("成功进入文章详情页面");
                    updateCurrentStatus("成功进入文章详情页，准备评论");

                    // 进行评论
                    updateCurrentStatus("正在进行评论");
                    if (performComment()) {
                        // 评论成功，记录文章控件信息
                        addArticleElementToCommented(articleElement);
                        successCount++;
                        addLog("评论成功！当前进度: " + successCount + "/" + commentCount);
                        addLog("已评论文章控件数量: " + commentedArticleElements.length);
                        updateCurrentStatus("评论成功！进度: " + successCount + "/" + commentCount);
                        updateCommentedArticleCount();
                        updateProcessedStepCount();
                    } else {
                        addLog("评论失败");
                        updateCurrentStatus("评论失败，继续下一篇");
                    }
                } else {
                    addLog("未能进入文章详情页面，跳过此文章");
                    updateCurrentStatus("未能进入文章详情页，跳过此文章");
                }

                // 返回搜索结果页面
                addLog("返回搜索结果页面");
                updateCurrentStatus("返回搜索结果页面");
                backToSearchResults();

                // 随机延时
                let randomDelay = delay + Math.floor(Math.random() * 3);
                addLog("等待 " + randomDelay + " 秒后继续");
                sleep(randomDelay * 1000);
            } else {
                addLog("点击文章失败");
                updateCurrentStatus("点击文章失败，继续查找");
            }
        } else {
            // 当前页面没有新的文章控件，向下滑动
            consecutiveScrollCount++;
            addLog("当前页面没有找到新的文章控件，向下滑动查找 (连续第" + consecutiveScrollCount + "次)");
            updateCurrentStatus("没有找到新文章，向下滑动查找 (第" + consecutiveScrollCount + "次)");

            // 添加调试信息
            addLog("调试信息 - 已找过文章数: " + foundArticleElements.length +
                   ", 已评论文章数: " + commentedArticleElements.length);

            if (consecutiveScrollCount >= maxConsecutiveScrolls) {
                addLog("连续滑动次数过多，可能已到达页面底部或所有文章都已处理");
                addLog("最终统计 - 总尝试次数: " + attemptCount +
                       ", 已找过文章: " + foundArticleElements.length +
                       ", 已评论文章: " + commentedArticleElements.length);
                updateCurrentStatus("已到达页面底部或所有文章都已处理");
                break;
            }

            updateCurrentStatus("正在向下滑动查找更多文章");
            scrollDownToFindMore();
            updateProcessedStepCount();
            sleep(3000); // 增加等待时间，确保内容加载
        }
    }

    if (successCount >= commentCount) {
        addLog("脚本执行完成，成功评论 " + successCount + " 篇文章");
        updateCurrentStatus("脚本执行完成，成功评论 " + successCount + " 篇文章");
        updateProcessedStepCount();
        // 移除toast调用，避免UI线程错误

        // 脚本执行完成后关闭小红书应用
        addLog("脚本执行完成，休眠5秒后关闭小红书应用");
        updateCurrentStatus("脚本执行完成，休眠5秒后关闭小红书应用");
        sleep(5000);
        closeXiaohongshuApp();
    } else if (attemptCount >= maxAttempts) {
        addLog("达到最大尝试次数，脚本停止。成功评论 " + successCount + " 篇文章");
        updateCurrentStatus("达到最大尝试次数，成功评论 " + successCount + " 篇文章");
        updateProcessedStepCount();
        // 移除toast调用，避免UI线程错误

        // 脚本达到最大尝试次数后关闭小红书应用
        addLog("脚本达到最大尝试次数，休眠5秒后关闭小红书应用");
        updateCurrentStatus("脚本达到最大尝试次数，休眠5秒后关闭小红书应用");
        sleep(5000);
        closeXiaohongshuApp();
    } else {
        addLog("脚本被用户终止，成功评论 " + successCount + " 篇文章");
        updateCurrentStatus("脚本被用户终止，成功评论 " + successCount + " 篇文章");
        updateProcessedStepCount();
        // 移除toast调用，避免UI线程错误
    }
}

// 启动小红书应用（统一入口函数）
function launchXiaohongshu(selectedApp) {
    addLog("=== 开始启动小红书应用 ===");

    if (selectedApp && selectedApp.trim() !== '') {
        addLog("使用指定应用启动: " + selectedApp);
        return launchXiaohongshuByName(selectedApp);
    } else {
        addLog("未指定应用名称，使用默认启动方式");
        return launchXiaohongshuDefault();
    }
}

// 确保在主页（通过分析页面控件判断）
function ensureInHomePage() {
    addLog("检查是否在主页");

    // 通过分析页面控件判断是否在首页
    if (!isInHomePage()) {
        addLog("不在首页，开始返回主页");
        backToHome();
    }

    addLog("已确保在主页");
    return true;
}

// 判断是否在首页（必须包含附近、关注、发现三个文本）
function isInHomePage() {
    // 减少日志输出，只在关键时刻记录
    // addLog("分析当前页面控件，检测首页特征");

    // 小红书首页必须包含的三个关键文本
    let requiredTexts = ["附近", "关注", "发现"];
    let foundTexts = [];

    // 检查每个必需的文本是否存在
    for (let i = 0; i < requiredTexts.length; i++) {
        let requiredText = requiredTexts[i];
        let found = false;

        // 通过text查找
        if (text(requiredText).exists()) {
            foundTexts.push(requiredText + "(text)");
            found = true;
            addLog("找到必需文本: " + requiredText + " (通过text)");
        }

        // 通过desc查找
        if (!found && desc(requiredText).exists()) {
            foundTexts.push(requiredText + "(desc)");
            found = true;
            addLog("找到必需文本: " + requiredText + " (通过desc)");
        }

        // 通过textContains查找
        if (!found && textContains(requiredText).exists()) {
            foundTexts.push(requiredText + "(contains)");
            found = true;
            addLog("找到必需文本: " + requiredText + " (通过contains)");
        }

        if (!found) {
            addLog("❌ 未找到必需文本: " + requiredText);
        }
    }

    // 必须找到所有三个文本才认为在首页
    let isHome = foundTexts.length >= 3;

    if (isHome) {
        addLog("✅ 确认在首页，找到所有必需文本: " + foundTexts.join(", "));
    } else {
        addLog("❌ 不在首页，缺少必需文本。已找到: " + foundTexts.join(", "));
        addLog("缺少的文本: " + requiredTexts.filter(text =>
            !foundTexts.some(found => found.includes(text))
        ).join(", "));

        // 额外检查：分析所有可见文本
        analyzeCurrentPageText();
    }

    return isHome;
}

// 分析当前页面的所有文本（用于调试）
function analyzeCurrentPageText() {
    // 减少日志输出
    // addLog("分析当前页面所有可见文本");

    try {
        // 获取所有TextView控件的文本
        let textViews = className("android.widget.TextView").find();
        let visibleTexts = [];

        for (let i = 0; i < Math.min(textViews.length, 20); i++) { // 限制最多分析20个控件
            let textView = textViews[i];
            let text = textView.text();

            if (text && text.trim().length > 0 && text.length < 20) { // 过滤掉过长的文本
                visibleTexts.push(text.trim());
            }
        }

        addLog("当前页面可见文本: " + visibleTexts.join(", "));

        // 检查是否包含小红书相关的关键词
        let xiaohongshuKeywords = ["小红书", "笔记", "种草", "好物", "美妆", "穿搭"];
        let foundKeywords = [];

        for (let i = 0; i < xiaohongshuKeywords.length; i++) {
            let keyword = xiaohongshuKeywords[i];
            if (visibleTexts.some(text => text.includes(keyword))) {
                foundKeywords.push(keyword);
            }
        }

        if (foundKeywords.length > 0) {
            addLog("发现小红书相关关键词: " + foundKeywords.join(", "));
        }

    } catch (e) {
        addLog("分析页面文本时出错: " + e.message);
    }
}

// 返回主页（使用页面控件分析判断）
function backToHome() {
    addLog("开始返回主页操作");

    // 持续按返回键直到通过控件分析确认在首页
    let attempts = 0;
    const maxAttempts = 10; // 最多尝试10次，避免无限循环

    while (!isInHomePage() && attempts < maxAttempts) {
        addLog("按返回键返回主页，尝试第 " + (attempts + 1) + " 次");
        back();
        sleep(1000);
        attempts++;

        // 检查是否已经到达首页
        if (isInHomePage()) {
            addLog("成功返回到首页");
            break;
        }

        // 如果按了几次返回键还没到首页，尝试其他方法
        if (attempts >= 3) {
            addLog("尝试点击首页按钮");
            clickHomeButton();
            sleep(2000);

            // 再次检查是否到达首页
            if (isInHomePage()) {
                addLog("通过点击首页按钮成功返回");
                break;
            }
        }
    }

    if (attempts >= maxAttempts) {
        addLog("返回主页超时，尝试重新启动应用");
        // 如果还是无法返回主页，可能需要重新启动应用
        launchXiaohongshu();
        sleep(3000);
    }

    addLog("返回主页操作完成");
}

// 点击首页按钮
function clickHomeButton() {
    addLog("尝试点击首页按钮");

    // 使用坐标点击首页（通常在底部导航栏左侧）
    let screenWidth = device.width;
    let screenHeight = device.height;

    // 小红书首页按钮通常在底部导航栏的左侧
    let homeX = screenWidth * 0.1; // 左侧10%位置
    let homeY = screenHeight * 0.95; // 底部90%位置

    addLog("使用坐标点击首页按钮: (" + Math.round(homeX) + ", " + Math.round(homeY) + ")");
    click(homeX, homeY);
    sleep(2000);

    return true;
}

// 执行搜索
function performSearch(keyword) {
    addLog("开始搜索操作");

    // 查找搜索按钮或搜索框
    let searchBtn = findSearchButton();
    if (!searchBtn) {
        addLog("未找到搜索按钮，尝试坐标点击");
        // 通常搜索按钮在右上角
        let screenWidth = device.width;
        let screenHeight = device.height;
        click(screenWidth * 0.9, screenHeight * 0.08);
        sleep(2000);
    } else {
        searchBtn.click();
        sleep(2000);
        addLog("点击搜索按钮");
    }

    // 等待搜索页面加载
    sleep(2000);

    // 查找搜索输入框并输入关键词
    if (!inputSearchKeyword(keyword)) {
        addLog("输入搜索关键词失败");
        return false;
    }

    // 执行搜索
    if (!executeSearch()) {
        addLog("执行搜索失败");
        return false;
    }

    // 等待搜索结果加载
    sleep(3000);
    addLog("搜索结果加载完成");
    return true;
}

// 查找搜索按钮
function findSearchButton() {
    // 方法1: 通过描述查找
    let searchBtn = desc("搜索").findOne(2000);
    if (searchBtn) {
        addLog("通过desc找到搜索按钮");
        return searchBtn;
    }

    // 方法2: 通过文本查找
    searchBtn = text("搜索").findOne(2000);
    if (searchBtn) {
        addLog("通过text找到搜索按钮");
        return searchBtn;
    }

    // 方法3: 查找搜索图标
    searchBtn = desc("搜索图标").findOne(2000) || desc("search").findOne(2000);
    if (searchBtn) {
        addLog("通过图标desc找到搜索按钮");
        return searchBtn;
    }

    // 方法4: 通过ID查找
    let possibleIds = [
        "com.xingin.xhs:id/search",
        "com.xingin.xhs:id/search_btn",
        "com.xingin.xhs:id/search_icon"
    ];

    for (let i = 0; i < possibleIds.length; i++) {
        searchBtn = id(possibleIds[i]).findOne(1000);
        if (searchBtn) {
            addLog("通过ID找到搜索按钮: " + possibleIds[i]);
            return searchBtn;
        }
    }

    return null;
}

// 输入搜索关键词
function inputSearchKeyword(keyword) {
    addLog("正在输入搜索关键词: " + keyword);

    // 方法1: 查找搜索输入框
    let searchInput = className("android.widget.EditText").findOne(3000);
    if (searchInput) {
        searchInput.click();
        sleep(1000);
        searchInput.setText(keyword);
        addLog("通过EditText输入关键词");
        return true;
    }

    // 方法2: 通过hint查找
    searchInput = textContains("搜索").className("android.widget.EditText").findOne(2000);
    if (searchInput) {
        searchInput.click();
        sleep(1000);
        searchInput.setText(keyword);
        addLog("通过hint找到输入框");
        return true;
    }

    // 方法3: 直接使用setText
    setText(keyword);
    addLog("使用setText输入关键词");
    return true;
}

// 执行搜索
function executeSearch() {
    addLog("执行搜索操作");

    // 方法1: 查找搜索按钮
    let searchBtn = text("搜索").findOne(2000) || desc("搜索").findOne(2000);
    if (searchBtn) {
        searchBtn.click();
        addLog("点击搜索按钮执行搜索");
        return true;
    }

    // 方法2: 按回车键
    key(66); // KEYCODE_ENTER
    addLog("按回车键执行搜索");
    return true;
}

// 返回主页并确保在首页页面
function backToHomeAndEnsureHomePage() {
    addLog("返回主页并确保在首页页面");

    // 第一步：确保在主页
    ensureInHomePage();

    // 第二步：确保在首页tab页面（而不是其他tab如发现、购物等）
    ensureInHomeTab();

    addLog("已确保在首页页面");
    return true;
}

// 确保在首页tab页面
function ensureInHomeTab() {
    addLog("确保在首页tab页面");

    // 检查是否已经在首页tab
    if (isInHomeTab()) {
        addLog("已在首页tab页面");
        return true;
    }

    // 点击首页tab按钮
    clickHomeButton();
    sleep(2000);

    // 再次检查
    if (isInHomeTab()) {
        addLog("成功切换到首页tab页面");
        return true;
    }

    addLog("无法确定是否在首页tab页面，继续执行");
    return true;
}

// 检查是否在首页tab页面（使用严格的三要素判断）
function isInHomeTab() {
    addLog("检查是否在首页tab页面");

    // 直接使用isInHomePage函数，因为首页tab就是首页
    // 必须包含"附近"、"关注"、"发现"三个文本
    return isInHomePage();
}

// 检查是否在搜索结果页面（通过分析页面控件判断）
function isInSearchResults() {
    // 减少日志输出
    // addLog("分析当前页面控件，检测搜索结果页面特征");

    // 小红书搜索结果页面必须包含的四个关键文本
    let requiredTexts = ["全部", "用户", "商品","综合"];
    let foundTexts = [];

    // 检查每个必需的文本是否存在
    for (let i = 0; i < requiredTexts.length; i++) {
        let requiredText = requiredTexts[i];
        let found = false;

        // 通过text查找
        if (text(requiredText).exists()) {
            foundTexts.push(requiredText + "(text)");
            found = true;
            addLog("找到搜索结果页面文本: " + requiredText + " (通过text)");
        }

        // 通过desc查找
        if (!found && desc(requiredText).exists()) {
            foundTexts.push(requiredText + "(desc)");
            found = true;
            addLog("找到搜索结果页面文本: " + requiredText + " (通过desc)");
        }

        // 通过textContains查找
        if (!found && textContains(requiredText).exists()) {
            foundTexts.push(requiredText + "(contains)");
            found = true;
            addLog("找到搜索结果页面文本: " + requiredText + " (通过contains)");
        }

        if (!found) {
            addLog("未找到搜索结果页面文本: " + requiredText);
        }
    }

    // 必须找到所有四个文本才认为在搜索结果页面
    let isSearchResults = foundTexts.length >= 4;

    if (isSearchResults) {
        addLog("✅ 确认在搜索结果页面，找到所有必需文本: " + foundTexts.join(", "));
    } else {
        addLog("❌ 不在搜索结果页面，缺少必需文本。已找到: " + foundTexts.join(", "));
        addLog("缺少的文本: " + requiredTexts.filter(text =>
            !foundTexts.some(found => found.includes(text))
        ).join(", "));

        // 额外检查：分析所有可见文本（用于调试）
        analyzeSearchPageText();
    }

    return isSearchResults;
}

// 分析搜索页面的所有文本（用于调试）
function analyzeSearchPageText() {
    // 减少日志输出
    // addLog("分析搜索页面所有可见文本");

    try {
        // 获取所有TextView控件的文本
        let textViews = className("android.widget.TextView").find();
        let visibleTexts = [];

        for (let i = 0; i < Math.min(textViews.length, 20); i++) { // 限制最多分析20个控件
            let textView = textViews[i];
            let text = textView.text();

            if (text && text.trim().length > 0 && text.length < 20) { // 过滤掉过长的文本
                visibleTexts.push(text.trim());
            }
        }

        addLog("搜索页面可见文本: " + visibleTexts.join(", "));

        // 检查是否包含搜索相关的关键词
        let searchKeywords = ["搜索", "结果", "找到", "笔记", "用户", "商品"];
        let foundKeywords = [];

        for (let i = 0; i < searchKeywords.length; i++) {
            let keyword = searchKeywords[i];
            if (visibleTexts.some(text => text.includes(keyword))) {
                foundKeywords.push(keyword);
            }
        }

        if (foundKeywords.length > 0) {
            addLog("找到搜索相关关键词: " + foundKeywords.join(", "));
        } else {
            addLog("未找到搜索相关关键词");
        }

    } catch (e) {
        addLog("分析搜索页面文本时出错: " + e.message);
    }
}

// 查找当前页面的文章控件（跳过已评论的文章）
function findCurrentPageArticle() {
    addLog("查找当前页面的新文章控件（跳过已评论文章）");

    try {
        // 使用多种方式查找文章元素，提高识别准确性

        // 方法1: 使用原有的选择器
        let textElements1 = id("0_resource_name_obfuscated")
            .className("android.widget.TextView")
            .find();

        // 方法2: 查找可点击的容器元素
        let clickableElements = className("android.view.ViewGroup")
            .clickable(true)
            .find();

        // 方法3: 查找包含文本的LinearLayout
        let layoutElements = className("android.widget.LinearLayout")
            .find();

        addLog("找到候选元素: TextView=" + textElements1.length +
               ", Clickable=" + clickableElements.length +
               ", Layout=" + layoutElements.length);

        // 合并所有候选元素
        let allElements = [];

        // 添加TextView元素
        for (let i = 0; i < textElements1.length; i++) {
            allElements.push({type: "TextView", element: textElements1[i]});
        }

        // 添加可点击元素中包含文本的
        for (let i = 0; i < clickableElements.length; i++) {
            let clickable = clickableElements[i];
            let textViews = clickable.find(className("android.widget.TextView"));
            if (textViews.length > 0) {
                allElements.push({type: "Clickable", element: clickable, textElement: textViews[0]});
            }
        }

        addLog("总共分析 " + allElements.length + " 个候选元素");

        for (let i = 0; i < allElements.length; i++) {
            let candidate = allElements[i];
            let element = candidate.element;
            let textElement = candidate.textElement || element;

            try {
                // 获取文本内容
                let articleText = "";
                if (candidate.type === "TextView") {
                    articleText = element.text();
                } else if (candidate.type === "Clickable" && candidate.textElement) {
                    articleText = candidate.textElement.text();
                }

                // 检查文本是否有效
                if (!articleText || articleText.trim().length < 5 || articleText.trim().length > 200) {
                    continue;
                }

                // 过滤掉明显不是文章标题的文本
                let invalidTexts = ["搜索", "发现", "关注", "我", "消息", "购物车", "首页", "推荐", "附近"];
                let isInvalidText = false;
                for (let j = 0; j < invalidTexts.length; j++) {
                    if (articleText.includes(invalidTexts[j]) && articleText.length < 10) {
                        isInvalidText = true;
                        break;
                    }
                }
                if (isInvalidText) {
                    continue;
                }

                // 确定文章容器
                let articleContainer = element;
                if (candidate.type === "TextView") {
                    // 对于TextView，尝试找到合适的父容器
                    let parent = element.parent();
                    if (parent && parent.bounds().height() > 100) {
                        articleContainer = parent;
                    }
                }

                // 检查容器大小
                if (!articleContainer ||
                    articleContainer.bounds().height() < 80 ||
                    articleContainer.bounds().width() < 100) {
                    continue;
                }

                let articleTitle = articleText.trim();

                // 创建文章控件信息对象
                let articleElementInfo = {
                    element: articleContainer,
                    text: articleTitle,
                    bounds: articleContainer.bounds(),
                    sourceElement: textElement,
                    type: candidate.type
                };

                // 检查这个文章控件是否已经找过
                if (isArticleElementAlreadyFoundLoose(articleElementInfo)) {
                    addLog("跳过已找过的文章: " + articleTitle.substring(0, 20) + "...");
                    continue;
                }

                // 检查这个文章控件是否已经评论过
                if (isArticleElementAlreadyCommentedLoose(articleElementInfo)) {
                    addLog("跳过已评论的文章: " + articleTitle.substring(0, 20) + "...");
                    addArticleElementToFound(articleElementInfo);
                    continue;
                }

                addLog("找到新的有效文章控件 (" + candidate.type + "): " + articleTitle.substring(0, 30) + "...");

                // 将找到的新文章控件记录到找过的列表中
                addArticleElementToFound(articleElementInfo);

                return articleElementInfo;

            } catch (e) {
                addLog("处理第 " + i + " 个元素时出错: " + e.message);
            }
        }

        addLog("当前页面未找到新的文章控件");
        return null;

    } catch (e) {
        addLog("查找文章控件时出错: " + e.message);
        return null;
    }
}

// 点击文章控件
function clickArticleElement(articleData) {
    addLog("点击文章控件: " + articleData.text.substring(0, 30) + "...");

    try {
        // 优先使用控件点击
        if (articleData.element && articleData.element.clickable()) {
            articleData.element.click();
            addLog("通过控件点击成功");
            return true;
        }

        // 使用坐标点击
        let bounds = articleData.bounds;
        let centerX = bounds.left + bounds.width() / 2;
        let centerY = bounds.top + bounds.height() / 2;
        click(centerX, centerY);
        addLog("通过坐标点击成功: (" + Math.round(centerX) + ", " + Math.round(centerY) + ")");
        return true;

    } catch (e) {
        addLog("点击文章控件失败: " + e.message);
        return false;
    }
}

// 执行评论操作
function performComment() {
    addLog("开始执行评论操作");

    try {
        // 查找并点击评论按钮
        if (!clickCommentButtonImproved()) {
            addLog("无法找到评论按钮");
            return false;
        }

        sleep(2000);

        // 输入评论内容
        if (!inputComment()) {
            addLog("输入评论失败");
            return false;
        }

        sleep(2000);
        return true;

    } catch (e) {
        addLog("执行评论操作时出错: " + e.message);
        return false;
    }
}

// 向下滑动查找更多文章
function scrollDownToFindMore() {
    addLog("向下滑动查找更多文章");

    let screenWidth = device.width;
    let screenHeight = device.height;

    // 记录滑动前的页面内容，用于判断是否真的加载了新内容
    let beforeScrollTexts = getPageTexts();

    // 执行多种滑动策略，确保加载新内容

    // 策略1: 大幅度滑动
    addLog("执行大幅度滑动");
    swipe(screenWidth * 0.5, screenHeight * 0.8, screenWidth * 0.5, screenHeight * 0.1, 1000);
    sleep(2000);

    // 策略2: 检查是否有新内容，如果没有则继续滑动
    let afterScrollTexts = getPageTexts();
    let hasNewContent = checkForNewContent(beforeScrollTexts, afterScrollTexts);

    if (!hasNewContent) {
        addLog("未检测到新内容，执行额外滑动");

        // 执行多次中等幅度滑动
        for (let i = 0; i < 3; i++) {
            swipe(screenWidth * 0.5, screenHeight * 0.7, screenWidth * 0.5, screenHeight * 0.3, 800);
            sleep(1500);
            addLog("执行第 " + (i + 1) + " 次额外滑动");
        }

        // 最后尝试快速滑动到底部再回来
        addLog("尝试快速滑动刷新");
        swipe(screenWidth * 0.5, screenHeight * 0.9, screenWidth * 0.5, screenHeight * 0.1, 500);
        sleep(1000);
        swipe(screenWidth * 0.5, screenHeight * 0.1, screenWidth * 0.5, screenHeight * 0.5, 500);
        sleep(2000);
    }

    addLog("滑动完成，等待新内容加载");
}

// 获取当前页面的文本内容（用于判断滑动是否加载了新内容）
function getPageTexts() {
    try {
        let texts = [];
        let textViews = className("android.widget.TextView").find();

        for (let i = 0; i < Math.min(textViews.length, 20); i++) {
            let text = textViews[i].text();
            if (text && text.trim().length > 3 && text.trim().length < 100) {
                texts.push(text.trim());
            }
        }

        return texts;
    } catch (e) {
        addLog("获取页面文本时出错: " + e.message);
        return [];
    }
}

// 检查是否有新内容
function checkForNewContent(beforeTexts, afterTexts) {
    try {
        // 简单检查：如果滑动后的文本数量明显增加，认为有新内容
        if (afterTexts.length > beforeTexts.length + 2) {
            addLog("检测到新内容：文本数量从 " + beforeTexts.length + " 增加到 " + afterTexts.length);
            return true;
        }

        // 检查是否有新的文本内容
        let newTextsCount = 0;
        for (let i = 0; i < afterTexts.length; i++) {
            let afterText = afterTexts[i];
            let found = false;
            for (let j = 0; j < beforeTexts.length; j++) {
                if (beforeTexts[j] === afterText) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                newTextsCount++;
            }
        }

        if (newTextsCount >= 3) {
            addLog("检测到 " + newTextsCount + " 个新文本内容");
            return true;
        }

        addLog("未检测到明显的新内容");
        return false;

    } catch (e) {
        addLog("检查新内容时出错: " + e.message);
        return false;
    }
}

// 检查文章控件是否已经找过
function isArticleElementAlreadyFound(articleElementInfo) {
    if (!articleElementInfo || !articleElementInfo.text) {
        return false;
    }

    let currentText = articleElementInfo.text.trim();
    let currentBounds = articleElementInfo.bounds;

    // 检查已找过的文章控件
    for (let i = 0; i < foundArticleElements.length; i++) {
        let foundElement = foundArticleElements[i];

        // 方法1: 文本完全匹配
        if (foundElement.text === currentText) {
            addLog("发现重复文章控件（已找过，文本匹配）: " + currentText.substring(0, 20) + "...");
            return true;
        }

        // 方法2: 坐标位置匹配（允许小幅偏移）
        if (foundElement.bounds && currentBounds) {
            let xDiff = Math.abs(foundElement.bounds.left - currentBounds.left);
            let yDiff = Math.abs(foundElement.bounds.top - currentBounds.top);

            if (xDiff < 20 && yDiff < 20) {
                addLog("发现重复文章控件（已找过，位置匹配）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }

        // 方法3: 文本部分匹配（前20个字符）
        if (currentText.length > 10 && foundElement.text.length > 10) {
            let currentShort = currentText.substring(0, 20);
            let foundShort = foundElement.text.substring(0, 20);

            if (currentShort === foundShort) {
                addLog("发现重复文章控件（已找过，部分文本匹配）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }
    }

    return false;
}

// 检查文章控件是否已经评论过
function isArticleElementAlreadyCommented(articleElementInfo) {
    if (!articleElementInfo || !articleElementInfo.text) {
        return false;
    }

    let currentText = articleElementInfo.text.trim();
    let currentBounds = articleElementInfo.bounds;

    // 检查已记录的文章控件
    for (let i = 0; i < commentedArticleElements.length; i++) {
        let commentedElement = commentedArticleElements[i];

        // 方法1: 文本完全匹配
        if (commentedElement.text === currentText) {
            addLog("发现重复文章控件（已评论，文本匹配）: " + currentText.substring(0, 20) + "...");
            return true;
        }

        // 方法2: 坐标位置匹配（允许小幅偏移）
        if (commentedElement.bounds && currentBounds) {
            let xDiff = Math.abs(commentedElement.bounds.left - currentBounds.left);
            let yDiff = Math.abs(commentedElement.bounds.top - currentBounds.top);

            if (xDiff < 20 && yDiff < 20) {
                addLog("发现重复文章控件（已评论，位置匹配）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }

        // 方法3: 文本部分匹配（前20个字符）
        if (currentText.length > 10 && commentedElement.text.length > 10) {
            let currentShort = currentText.substring(0, 20);
            let commentedShort = commentedElement.text.substring(0, 20);

            if (currentShort === commentedShort) {
                addLog("发现重复文章控件（已评论，部分文本匹配）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }
    }

    return false;
}

// 更宽松的检查文章控件是否已经找过（主要基于文本匹配，忽略位置变化）
function isArticleElementAlreadyFoundLoose(articleElementInfo) {
    if (!articleElementInfo || !articleElementInfo.text) {
        return false;
    }

    let currentText = articleElementInfo.text.trim();

    // 检查已找过的文章控件，主要使用文本匹配
    for (let i = 0; i < foundArticleElements.length; i++) {
        let foundElement = foundArticleElements[i];

        // 方法1: 文本完全匹配
        if (foundElement.text === currentText) {
            addLog("发现重复文章（完全匹配）: " + currentText.substring(0, 20) + "...");
            return true;
        }

        // 方法2: 文本部分匹配（前12个字符，更严格）
        if (currentText.length > 12 && foundElement.text.length > 12) {
            let currentShort = currentText.substring(0, 12);
            let foundShort = foundElement.text.substring(0, 12);

            if (currentShort === foundShort) {
                addLog("发现重复文章（前缀匹配）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }

        // 方法3: 检查文本相似度（使用更严格的标准）
        if (currentText.length > 8 && foundElement.text.length > 8) {
            let similarity = calculateTextSimilarity(currentText, foundElement.text);
            if (similarity > 0.8) { // 相似度超过80%认为是同一篇文章
                addLog("发现重复文章（相似度 " + Math.round(similarity * 100) + "%）: " + currentText.substring(0, 20) + "...");
                return true;
            }
        }

        // 方法4: 检查是否包含相同的关键词组合
        if (currentText.length > 10 && foundElement.text.length > 10) {
            let currentKeywords = extractKeywords(currentText);
            let foundKeywords = extractKeywords(foundElement.text);

            if (currentKeywords.length > 0 && foundKeywords.length > 0) {
                let matchCount = 0;
                for (let j = 0; j < currentKeywords.length; j++) {
                    for (let k = 0; k < foundKeywords.length; k++) {
                        if (currentKeywords[j] === foundKeywords[k]) {
                            matchCount++;
                        }
                    }
                }

                // 如果关键词匹配度超过70%，认为是同一篇文章
                let matchRatio = matchCount / Math.max(currentKeywords.length, foundKeywords.length);
                if (matchRatio > 0.7) {
                    addLog("发现重复文章（关键词匹配 " + Math.round(matchRatio * 100) + "%）: " + currentText.substring(0, 20) + "...");
                    return true;
                }
            }
        }
    }

    return false;
}

// 计算两个文本的相似度
function calculateTextSimilarity(text1, text2) {
    try {
        // 简单的字符级相似度计算
        let longer = text1.length > text2.length ? text1 : text2;
        let shorter = text1.length > text2.length ? text2 : text1;

        if (longer.length === 0) {
            return 1.0;
        }

        let editDistance = getEditDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    } catch (e) {
        return 0;
    }
}

// 计算编辑距离
function getEditDistance(str1, str2) {
    let matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
}

// 提取文本中的关键词
function extractKeywords(text) {
    try {
        // 移除标点符号，分割成词
        let words = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '').split(/\s+/);
        let keywords = [];

        for (let i = 0; i < words.length; i++) {
            let word = words[i].trim();
            if (word.length >= 2) { // 只保留长度>=2的词
                keywords.push(word);
            }
        }

        return keywords;
    } catch (e) {
        return [];
    }
}

// 更宽松的检查文章控件是否已经评论过（主要基于文本匹配，忽略位置变化）
function isArticleElementAlreadyCommentedLoose(articleElementInfo) {
    if (!articleElementInfo || !articleElementInfo.text) {
        return false;
    }

    let currentText = articleElementInfo.text.trim();

    // 检查已记录的文章控件，主要使用文本匹配
    for (let i = 0; i < commentedArticleElements.length; i++) {
        let commentedElement = commentedArticleElements[i];

        // 方法1: 文本完全匹配
        if (commentedElement.text === currentText) {
            return true;
        }

        // 方法2: 文本部分匹配（前15个字符）
        if (currentText.length > 10 && commentedElement.text.length > 10) {
            let currentShort = currentText.substring(0, 15);
            let commentedShort = commentedElement.text.substring(0, 15);

            if (currentShort === commentedShort) {
                return true;
            }
        }

        // 方法3: 检查是否包含相同的关键词（至少3个字符）
        if (currentText.length > 6 && commentedElement.text.length > 6) {
            let currentWords = currentText.split(/\s+/);
            let commentedWords = commentedElement.text.split(/\s+/);

            for (let j = 0; j < currentWords.length; j++) {
                let currentWord = currentWords[j];
                if (currentWord.length >= 3) {
                    for (let k = 0; k < commentedWords.length; k++) {
                        let commentedWord = commentedWords[k];
                        if (commentedWord.length >= 3 && currentWord === commentedWord) {
                            // 如果有相同的关键词且长度足够，认为是同一篇文章
                            return true;
                        }
                    }
                }
            }
        }
    }

    return false;
}

// 将文章控件添加到找过列表
function addArticleElementToFound(articleElementInfo) {
    if (articleElementInfo && articleElementInfo.text && articleElementInfo.text.trim().length > 0) {
        // 创建要保存的控件信息
        let elementToSave = {
            text: articleElementInfo.text.trim(),
            bounds: {
                left: articleElementInfo.bounds.left,
                top: articleElementInfo.bounds.top,
                right: articleElementInfo.bounds.right,
                bottom: articleElementInfo.bounds.bottom
            },
            timestamp: new Date().getTime()
        };

        foundArticleElements.push(elementToSave);
        addLog("记录找过的文章控件: " + articleElementInfo.text.substring(0, 30) + "...");
        addLog("当前找过的文章控件总数: " + foundArticleElements.length);

        // 限制记录数量，避免内存占用过多
        if (foundArticleElements.length > 200) {
            foundArticleElements.shift(); // 移除最早的记录
            addLog("找过的文章控件记录超过200条，移除最早记录");
        }
    }
}

// 将文章控件添加到已评论列表
function addArticleElementToCommented(articleElementInfo) {
    if (articleElementInfo && articleElementInfo.text && articleElementInfo.text.trim().length > 0) {
        // 创建要保存的控件信息
        let elementToSave = {
            text: articleElementInfo.text.trim(),
            bounds: {
                left: articleElementInfo.bounds.left,
                top: articleElementInfo.bounds.top,
                right: articleElementInfo.bounds.right,
                bottom: articleElementInfo.bounds.bottom
            },
            timestamp: new Date().getTime()
        };

        commentedArticleElements.push(elementToSave);
        addLog("记录已评论文章控件: " + articleElementInfo.text.substring(0, 30) + "...");
        addLog("当前已评论文章控件总数: " + commentedArticleElements.length);

        // 限制记录数量，避免内存占用过多
        if (commentedArticleElements.length > 100) {
            commentedArticleElements.shift(); // 移除最早的记录
            addLog("已评论文章控件记录超过100条，移除最早记录");
        }
    }
}

// 返回搜索结果页面（改进版本）
function backToSearchResults() {
    addLog("开始返回搜索结果页面");

    let attempts = 0;
    const maxAttempts = 5;

    while (!isInSearchResults() && attempts < maxAttempts) {
        addLog("尝试返回搜索结果，第 " + (attempts + 1) + " 次");

        // 方法1: 按返回键
        back();
        sleep(1500);
        attempts++;

        // 检查是否已经回到搜索结果页面
        if (isInSearchResults()) {
            addLog("成功返回搜索结果页面");
            return true;
        }

        // 如果还没回到搜索结果页面，尝试其他方法
        if (attempts >= 2) {
            // 方法2: 尝试点击返回按钮
            let backBtn = desc("返回").findOne(1000) || desc("back").findOne(1000) ||
                         text("返回").findOne(1000) || textContains("返回").findOne(1000);
            if (backBtn) {
                addLog("找到返回按钮，点击返回");
                backBtn.click();
                sleep(1500);
                continue;
            }

            // 方法3: 使用坐标点击返回按钮（通常在左上角）
            let screenWidth = device.width;
            let screenHeight = device.height;
            addLog("使用坐标点击返回按钮");
            click(screenWidth * 0.05, screenHeight * 0.08);
            sleep(1500);
        }
    }

    // 如果还是无法返回搜索结果页面，尝试重新进入搜索
    if (!isInSearchResults()) {
        addLog("无法返回搜索结果页面，尝试重新进入搜索");
        ensureInHomePage();
        sleep(2000);
        return false; // 返回false表示需要重新搜索
    }

    addLog("已成功返回搜索结果页面");
    return true;
}

// 点击输入框确认位置
function clickInputConfirmPosition() {
    addLog("点击输入框确认位置");

    try {
        let screenWidth = device.width;
        let screenHeight = device.height;

        // 第一步：点击坐标（屏幕宽度的0.19，屏幕高度的0.51）
        let clickX1 = screenWidth * 0.19;
        let clickY1 = screenHeight * 0.55;

        click(clickX1, clickY1);
        addLog("第一步点击完成: (" + Math.round(clickX1) + ", " + Math.round(clickY1) + ")");
        sleep(500);

        // 第二步：点击坐标（屏幕宽度的0.08，屏幕高度的0.71）
        let clickX2 = screenWidth * 0.08;
        let clickY2 = screenHeight * 0.75;

        click(clickX2, clickY2);
        addLog("第二步点击完成: (" + Math.round(clickX2) + ", " + Math.round(clickY2) + ")");
        sleep(500);

        // 第三步：查找并点击发送按钮
        let sendButton = id("0_resource_name_obfuscated").className("android.widget.TextView").text("发送").findOne(3000);
        if (sendButton) {
            sendButton.click();
            addLog("第三步点击发送按钮成功");
        } else {
            addLog("第三步未找到发送按钮");
        }
        sleep(500);

        addLog("输入框确认位置三步操作完成");
        return true;

    } catch (e) {
        addLog("点击输入框确认位置失败: " + e.message);
        return false;
    }
}

// 判断是否在文章详情页面
function isInArticleDetailPage() {
    addLog("判断是否在文章详情页面");

    try {
        // 检查页面是否包含"说点什么"、"购买"或"评论"文本
        let hasKeywords = textContains("说点什么").exists() ||
                         textContains("购买").exists() ||
                         textContains("评论").exists();

        if (hasKeywords) {
            addLog("检测到文章详情页面关键词");
            return true;
        } else {
            addLog("未检测到文章详情页面关键词");
            return false;
        }

    } catch (e) {
        addLog("判断文章详情页面时出错: " + e.message);
        return false;
    }
}

// 简化的评论按钮点击函数
function clickCommentButtonImproved() {
    addLog("直接使用坐标点击评论按钮");

    // 使用坐标点击（评论按钮通常在底部右侧）
    let screenWidth = device.width;
    let screenHeight = device.height;
    click(screenWidth * 0.9, screenHeight * 0.95);
    addLog("坐标点击评论按钮完成");
    return true;
}

// 分析当前页面控件
function analyzeCurrentPageControls() {
    // 减少日志输出
    // addLog("开始分析当前页面控件");

    try {
        // 分析TextView控件
        let textViews = className("android.widget.TextView").find();
        // 减少日志输出
        // addLog("找到 " + textViews.length + " 个TextView控件");

        let relevantTexts = [];
        for (let i = 0; i < Math.min(textViews.length, 20); i++) {
            let textView = textViews[i];
            let text = textView.text();
            if (text && text.trim().length > 0) {
                // 记录包含关键词的文本
                if (text.includes("说点什么") || text.includes("评论") || text.includes("发送") ||
                    text.includes("购买") || text.includes("点赞") || text.includes("收藏")) {
                    relevantTexts.push(text);
                    addLog("发现相关文本: " + text);
                }
            }
        }

        // 分析EditText控件
        let editTexts = className("android.widget.EditText").find();
        addLog("找到 " + editTexts.length + " 个EditText控件");

        for (let i = 0; i < editTexts.length; i++) {
            let editText = editTexts[i];
            let hint = editText.hint();
            if (hint) {
                addLog("发现EditText提示: " + hint);
            }
        }

        // 分析LinearLayout控件（评论相关）
        let linearLayouts = className("android.widget.LinearLayout").clickable(true).find();
        addLog("找到 " + linearLayouts.length + " 个可点击的LinearLayout控件");

        // 分析Button控件
        let buttons = className("android.widget.Button").find();
        addLog("找到 " + buttons.length + " 个Button控件");

        for (let i = 0; i < buttons.length; i++) {
            let button = buttons[i];
            let text = button.text();
            if (text) {
                addLog("发现Button文本: " + text);
            }
        }

        // 减少日志输出
        // addLog("页面控件分析完成");
        return {
            textViews: textViews.length,
            editTexts: editTexts.length,
            linearLayouts: linearLayouts.length,
            buttons: buttons.length,
            relevantTexts: relevantTexts
        };

    } catch (e) {
        addLog("分析页面控件时出错: " + e.message);
        return null;
    }
}

// 输入评论内容
function inputComment() {
    addLog("开始输入评论，当前模式: " + CONFIG.commentMode);
    addLog("CONFIG对象内容: " + JSON.stringify(CONFIG));

    // 根据评论模式选择不同的处理方式
    if (CONFIG.commentMode === "text") {
        addLog("执行文本评论模式，评论内容: " + CONFIG.commentText);
        // 文本评论模式
        return inputTextComment();
    } else {
        addLog("执行默认表情模式");
        // 默认表情模式
        return inputEmojiComment();
    }
}

// 文本评论模式
function inputTextComment() {
    addLog("=== 开始执行文本评论模式 ===");
    addLog("评论内容: " + CONFIG.commentText);
    addLog("CONFIG.commentMode: " + CONFIG.commentMode);

    // 先分析当前页面控件
    let pageAnalysis = analyzeCurrentPageControls();

    // 方法1: 查找评论输入框（TextView类型）
    let commentInput = id("0_resource_name_obfuscated").className("android.widget.TextView").text("说点什么...").findOne(2000);
    if (commentInput) {
        addLog("方法1: 找到TextView类型的评论输入框");
        commentInput.click();
        sleep(1000);

        // 输入文本内容
        let success = inputTextToCommentBox();
        if (success) {
            addLog("通过TextView控件输入文本评论成功");
            return true;
        } else {
            addLog("通过TextView控件输入文本评论失败");
            return false;
        }
    }

    // 方法2: 查找评论输入框（LinearLayout类型）
    let commentInput2 = id("0_resource_name_obfuscated").className("android.widget.LinearLayout").clickable(true).depth(11).findOne(2000);
    if (commentInput2) {
        addLog("方法2: 找到LinearLayout类型的评论输入框");
        commentInput2.click();
        sleep(1000);

        // 输入文本内容
        let success = inputTextToCommentBox();
        if (success) {
            addLog("通过LinearLayout控件输入文本评论成功");
            return true;
        } else {
            addLog("通过LinearLayout控件输入文本评论失败");
            return false;
        }
    }

    // 方法3: 查找包含"评论"或"说点什么"的EditText输入框
    let commentInput3 = textContains("说点什么").className("android.widget.EditText").findOne(2000) ||
                        textContains("评论").className("android.widget.EditText").findOne(2000);
    if (commentInput3) {
        addLog("方法3: 找到包含关键词的EditText输入框");
        commentInput3.click();
        sleep(1000);

        // 输入文本内容
        let success = inputTextToCommentBox();
        if (success) {
            addLog("通过EditText控件输入文本评论成功");
            return true;
        } else {
            addLog("通过EditText控件输入文本评论失败");
            return false;
        }
    }

    // 方法4: 根据页面分析结果查找输入框
    if (pageAnalysis && pageAnalysis.relevantTexts.length > 0) {
        addLog("方法4: 根据页面分析结果查找输入框");
        for (let i = 0; i < pageAnalysis.relevantTexts.length; i++) {
            let text = pageAnalysis.relevantTexts[i];
            if (text.includes("说点什么")) {
                let element = textContains(text).findOne(1000);
                if (element) {
                    addLog("找到分析结果中的输入框: " + text);
                    element.click();
                    sleep(1000);

                    let success = inputTextToCommentBox();
                    if (success) {
                        addLog("通过页面分析找到输入框并输入文本评论成功");
                        return true;
                    }
                }
            }
        }
    }

    // 方法5: 使用坐标点击输入框区域
    addLog("方法5: 使用坐标点击输入框区域");
    let screenWidth = device.width;
    let screenHeight = device.height;
    click(screenWidth * 0.5, screenHeight * 0.95);
    sleep(1000);

    // 输入文本内容
    let success = inputTextToCommentBox();
    if (success) {
        addLog("使用坐标点击输入框并输入文本评论成功");
        return true;
    } else {
        addLog("使用坐标点击输入框并输入文本评论失败");
        return false;
    }
}

// ==================== 关闭小红书应用 ====================

// 关闭小红书APP
function closeXiaohongshuApp() {
    addLog("开始关闭小红书APP");

    try {
        // 检查当前是否在小红书
        let currentPkg = currentPackage();
        addLog("当前应用包名: " + (currentPkg || "无"));

        if (currentPkg && currentPkg.includes("xhs")) {
            addLog("检测到小红书正在运行，开始关闭");

            // 方法1: 使用返回键多次退出
            addLog("使用返回键方式关闭应用");
            for (let i = 0; i < 15; i++) {
                back();
                sleep(300);

                // 检查是否已经退出到桌面
                let pkg = currentPackage();
                if (!pkg || pkg.includes("launcher") || pkg.includes("home") || pkg.includes("desktop")) {
                    addLog("✅ 已退出到桌面，当前应用: " + (pkg || "桌面"));
                    return true;
                }

                // 如果还在小红书，继续按返回键
                if (pkg && pkg.includes("xhs")) {
                    addLog("仍在小红书中，继续按返回键 (" + (i + 1) + "/15)");
                } else {
                    addLog("已切换到其他应用: " + pkg);
                    break;
                }
            }
        }

        // 方法2: 按Home键确保回到桌面
        addLog("按Home键返回桌面");
        home();
        sleep(1000);

        // 再次检查当前应用
        let finalPkg = currentPackage();
        addLog("最终应用包名: " + (finalPkg || "桌面"));

        if (!finalPkg || finalPkg.includes("launcher") || finalPkg.includes("home") || finalPkg.includes("desktop")) {
            addLog("✅ 小红书APP关闭成功");
        } else if (finalPkg.includes("xhs")) {
            addLog("⚠️ 小红书可能仍在运行，但已尽力关闭");
        } else {
            addLog("✅ 已切换到其他应用，小红书关闭成功");
        }

        return true;

    } catch (e) {
        addLog("❌ 关闭小红书APP时出错: " + e.message);
        // 备用方法：直接按Home键
        addLog("使用备用方法：按Home键");
        home();
        sleep(1000);
        return true;
    }
}

// 表情评论模式（原有的三步点击方法）
function inputEmojiComment() {
    addLog("使用表情评论模式");

    // 先分析当前页面控件
    let pageAnalysis = analyzeCurrentPageControls();

    // 方法1: 查找评论输入框（TextView类型）
    let commentInput = id("0_resource_name_obfuscated").className("android.widget.TextView").text("说点什么...").findOne(2000);
    if (commentInput) {
        addLog("方法1: 找到TextView类型的评论输入框");
        console.log("说点什么...进入评论");

        commentInput.click();
        sleep(1000);

        // 点击输入框后，调用确认位置方法（包含发送操作）
        let success = clickInputConfirmPosition();
        if (success) {
            addLog("通过TextView控件输入表情评论成功");
            return true;
        } else {
            addLog("通过TextView控件输入表情评论失败");
            return false;
        }
    }

    // 方法2: 查找评论输入框（LinearLayout类型）
    let commentInput2 = id("0_resource_name_obfuscated").className("android.widget.LinearLayout").clickable(true).depth(11).findOne(2000);
    if (commentInput2) {
        addLog("方法2: 找到LinearLayout类型的评论输入框");
        console.log("视频 说点什么...进入评论");

        commentInput2.click();
        sleep(1000);

        // 点击输入框后，调用确认位置方法（包含发送操作）
        let success = clickInputConfirmPosition();
        if (success) {
            addLog("通过LinearLayout控件输入表情评论成功");
            return true;
        } else {
            addLog("通过LinearLayout控件输入表情评论失败");
            return false;
        }
    }

    // 方法3: 查找包含"评论"或"说点什么"的EditText输入框
    let commentInput3 = textContains("说点什么").className("android.widget.EditText").findOne(2000) ||
                        textContains("评论").className("android.widget.EditText").findOne(2000);
    if (commentInput3) {
        addLog("方法3: 找到包含关键词的EditText输入框");
        commentInput3.click();
        sleep(1000);

        // 点击输入框后，调用确认位置方法（包含发送操作）
        let success = clickInputConfirmPosition();
        if (success) {
            addLog("通过EditText控件输入表情评论成功");
            return true;
        } else {
            addLog("通过EditText控件输入表情评论失败");
            return false;
        }
    }

    // 方法4: 根据页面分析结果查找输入框
    if (pageAnalysis && pageAnalysis.relevantTexts.length > 0) {
        addLog("方法4: 根据页面分析结果查找输入框");
        for (let i = 0; i < pageAnalysis.relevantTexts.length; i++) {
            let text = pageAnalysis.relevantTexts[i];
            if (text.includes("说点什么")) {
                let element = textContains(text).findOne(1000);
                if (element) {
                    addLog("找到分析结果中的输入框: " + text);
                    element.click();
                    sleep(1000);

                    let success = clickInputConfirmPosition();
                    if (success) {
                        addLog("通过页面分析找到输入框并输入表情评论成功");
                        return true;
                    }
                }
            }
        }
    }

    // 方法5: 使用坐标点击输入框区域
    addLog("方法5: 使用坐标点击输入框区域");
    let screenWidth = device.width;
    let screenHeight = device.height;
    click(screenWidth * 0.5, screenHeight * 0.95);
    sleep(1000);

    // 点击输入框后，调用确认位置方法（包含发送操作）
    let success = clickInputConfirmPosition();
    if (success) {
        addLog("使用坐标点击输入框并输入表情评论成功");
        return true;
    } else {
        addLog("使用坐标点击输入框并输入表情评论失败");
        return false;
    }
}

// 输入文本到评论框
function inputTextToCommentBox() {
    addLog("=== 开始输入文本到评论框 ===");
    addLog("要输入的文本: " + CONFIG.commentText);
    addLog("文本长度: " + (CONFIG.commentText ? CONFIG.commentText.length : 0));

    try {
        // 方法1: 查找EditText输入框并输入文本
        let editText = className("android.widget.EditText").findOne(3000);
        if (editText) {
            addLog("找到EditText输入框，输入文本");
            editText.click();
            sleep(500);
            editText.setText(CONFIG.commentText);
            sleep(1000);

            // 查找并点击发送按钮
            let sendButton = text("发送").findOne(2000) || desc("发送").findOne(2000);
            if (sendButton) {
                sendButton.click();
                addLog("点击发送按钮成功");
                sleep(1000);
                return true;
            } else {
                // 如果没找到发送按钮，尝试按回车键
                addLog("未找到发送按钮，尝试按回车键");
                key(66); // KEYCODE_ENTER
                sleep(1000);
                return true;
            }
        }

        // 方法2: 直接使用setText输入文本
        addLog("方法2: 直接使用setText输入文本");
        setText(CONFIG.commentText);
        sleep(1000);

        // 查找并点击发送按钮
        let sendButton = text("发送").findOne(2000) || desc("发送").findOne(2000);
        if (sendButton) {
            sendButton.click();
            addLog("点击发送按钮成功");
            sleep(1000);
            return true;
        } else {
            // 如果没找到发送按钮，尝试按回车键
            addLog("未找到发送按钮，尝试按回车键");
            key(66); // KEYCODE_ENTER
            sleep(1000);
            return true;
        }

    } catch (e) {
        addLog("输入文本到评论框失败: " + e.message);
        return false;
    }
}

// 启动脚本 - 移除main()调用，改为由服务器注入的代码执行
// main();