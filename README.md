# 🚀 Auto.js云群控系统

## 📋 项目概述

基于Node.js + Vue.js的现代化云群控自动化系统，支持小红书和闲鱼平台的自动化操作。采用前后端分离架构，支持1000+设备并发控制，具备完整的用户权限管理、实时通信、任务调度等企业级功能。

## 🏗️ 系统架构

- **前端**：Vue.js 2.6.14 + Element UI 2.15.13
- **后端**：Node.js + Express 4.18.2 + Socket.IO 4.8.1
- **数据库**：MySQL 8.0
- **设备端**：Android + Auto.js Pro

## 📁 项目结构

```
群控系统/
├── 📁 web/                          # 前端项目 (Vue.js)
│   ├── 📁 src/components/           # Vue组件 (13个功能组件)
│   ├── 📁 src/views/               # 页面组件 (13个页面)
│   ├── 📁 src/store/               # Vuex状态管理 (6个模块)
│   └── 📁 src/utils/               # 工具函数
├── 📁 server/                       # 后端项目 (Node.js)
│   ├── 📁 core/                    # 核心模块 (4个)
│   ├── 📁 device/                  # 设备管理 (3个)
│   ├── 📁 xiaohongshu/             # 小红书模块 (1个)
│   ├── 📁 xianyu/                  # 闲鱼模块 (1个)
│   └── 📄 server-main.js           # 模块化服务器入口
├── 📁 jb/                          # 小红书脚本 (13个)
├── 📁 xy-jb/                       # 闲鱼脚本 (1个)
├── 📁 scripts/                     # 工具脚本 (2个)
├── 📄 test-server.js               # 测试服务器 (单文件版本)
└── 📄 初始化数据库.sql              # 数据库初始化脚本
```

## 🚀 快速开始

### 1. 数据库初始化
```sql
-- 导入数据库脚本
mysql -u root -p < 初始化数据库.sql
```

### 2. 后端启动
```bash
# 安装依赖
npm install

# 启动服务器 (推荐使用测试服务器)
node test-server.js

# 或使用模块化服务器
node server/server-main.js
```

### 3. 前端启动
```bash
cd web
npm install
npm run serve
```

### 4. 访问系统
- Web管理界面：http://localhost:8080
- 默认账户：admin / admin123
- 测试连接码：TEST1234

## 🎯 核心功能

### 📸 小红书自动化 (7大功能)
1. **修改资料** - 昵称、简介、头像自动修改
2. **搜索加群** - 关键词搜索群聊并自动加群发消息
3. **循环群发** - 定时循环向群聊发送消息
4. **文章评论** - 搜索文章并自动评论
5. **手动UID私信** - 手动输入UID列表批量私信
6. **文件UID私信** - 上传UID文件批量私信
7. **视频发布** - 批量上传和发布视频

### 🐟 闲鱼自动化 (1大功能)
1. **关键词私信** - 搜索关键词商品并自动私信卖家

### 🔧 系统管理功能
- **设备管理** - 设备连接、状态监控、批量操作
- **脚本管理** - 脚本上传、编辑、版本控制
- **文件管理** - UID文件、视频文件上传管理
- **日志管理** - 执行日志、私聊记录、统计分析
- **用户认证** - 登录验证、权限控制、数据隔离

## 📊 系统规模

- **总代码量**：约25,000+行
- **API接口**：200+个
- **数据库表**：19个
- **并发设备**：1000+设备同时连接
- **实时通信**：WebSocket延迟 <50ms

## 🛡️ 安全特性

- JWT Token认证 + 密码加密
- 多租户数据隔离
- 设备连接码系统
- 权限控制和操作审计
- HTTPS传输加密

## 📚 文档

- [系统架构总览](./系统架构总览.md)
- [服务器系统文件功能明细](./服务器系统文件功能明细.md)
- [前端系统文件功能明细](./前端系统文件功能明细.md)
- [宝塔面板部署流程](./宝塔面板部署流程.md)
- [快速部署指南](./快速部署指南.md)
- [设备连接码使用说明](./设备连接码使用说明.md)

## 🔧 技术支持

如有问题，请参考相关文档或联系技术支持。

---

**Auto.js云群控系统** - 专业的移动设备自动化管理平台
